// 编辑弹窗配置
import { USER_UNIT_LIST, MACHINE_UNIT_LIST } from '../constants'

/**
 * 生成通用字段配置
 * @param {boolean} isAcceptance - 是否为验收模式
 * @param {boolean} isViewMode - 是否为查看模式
 * @returns {Array} 字段配置数组
 */
function createCommonFields(isAcceptance, isViewMode) {
  return [
    {
      key: "planNum",
      label: "计划数量",
      type: "input",
      required: !isAcceptance,
      inputType: "number",
      disable: isAcceptance,
    },
    {
      key: "realNum",
      label: "实际数量",
      type: "input",
      required: true,
      inputType: "number",
      isNotShow: !isAcceptance,
    },
    {
      key: "unitPrice",
      label: "单价（元）",
      type: "input",
      inputType: "number",
    },
    {
      key: "planTotal",
      label: "预计花费",
      type: isAcceptance ? "planTotal" : "countCost",
      readonly: true,
    },
    {
      key: "realTotal",
      label: "实际花费",
      type: isAcceptance ? (isViewMode ? "realTotal" : "countCost") : "",
      readonly: true,
      isNotShow: !isAcceptance,
    },
  ];
}

/**
 * 用户编辑配置
 * @param {Object} item - 编辑项数据
 * @param {number} idx - 索引
 * @param {boolean} isAcceptance - 是否为验收模式
 * @param {boolean} isViewMode - 是否为查看模式
 * @returns {Object} 编辑配置
 */
export function createUserEditConfig(item, idx, isAcceptance, isViewMode) {
  return {
    type: "users",
    idx,
    title: "人员编辑",
    fields: [
      { key: "units", label: "单位", type: "unit", required: true },
      ...createCommonFields(isAcceptance, isViewMode),
    ],
    model: { ...item, originId: item.originId || item.id },
    unitList: USER_UNIT_LIST,
    showDelete: item.originId ? false : true,
    info: {
      avatarText: item.userName?.slice(-2) || item.name?.slice(-2),
      title: item.userName || item.name,
      sub: item.deptName,
    },
  };
}

/**
 * 作业内容编辑配置
 * @param {Object} item - 编辑项数据
 * @param {number} idx - 索引
 * @param {boolean} isAcceptance - 是否为验收模式
 * @param {boolean} isViewMode - 是否为查看模式
 * @returns {Object} 编辑配置
 */
export function createSettlementEditConfig(item, idx, isAcceptance, isViewMode) {
  return {
    type: "settlements",
    idx,
    title: "作业内容编辑",
    fields: [
      { key: "units", label: "单位", type: "unit", required: false },
      {
        key: "planNumber",
        label: "计划数量",
        type: "input",
        required: !isAcceptance,
        inputType: "number",
        disable: isAcceptance,
      },
      {
        key: "realNumber",
        label: "实际数量",
        type: "input",
        required: true,
        inputType: "number",
        isNotShow: !isAcceptance,
      },
      {
        key: "unitPrice",
        label: "单价（元）",
        type: "input",
        inputType: "number",
        disable: true,
      },
      {
        key: "planTotal",
        label: "预计花费",
        type: isAcceptance ? "planTotal" : "countCost",
        readonly: true,
      },
      {
        key: "realTotal",
        label: "实际花费",
        type:  isAcceptance ? (isViewMode ? "realTotal" : "countCost") : "",
        readonly: true,
        isNotShow: !isAcceptance,
      },
    ],
    model: { ...item, originId: item.originId || item.id },
    unitList: [{ value: item.units, label: item.unitName || item.units }],
    showDelete: item.originId ? false : true,
    info: {
      title: item.codeTitle || item.settlementName,
      sub: item.desc,
    },
  };
}

/**
 * 材料编辑配置
 * @param {Object} item - 编辑项数据
 * @param {number} idx - 索引
 * @param {boolean} isAcceptance - 是否为验收模式
 * @param {boolean} isViewMode - 是否为查看模式
 * @returns {Object} 编辑配置
 */
export function createMaterialEditConfig(item, idx, isAcceptance, isViewMode) {
  return {
    type: "materials",
    idx,
    title: "材料编辑",
    fields: [
      { key: "units", label: "单位", type: "unit", required: false },
      ...createCommonFields(isAcceptance, isViewMode),
    ],
    model: { ...item, originId: item.originId || item.id },
    unitList: [{ value: item.units, label: item.unitName }],
    showDelete: item.originId ? false : true,
    info: {
      title: item.materialName,
      sub: item.amount,
    },
  };
}

/**
 * 机械编辑配置
 * @param {Object} item - 编辑项数据
 * @param {number} idx - 索引
 * @param {boolean} isAcceptance - 是否为验收模式
 * @param {boolean} isViewMode - 是否为查看模式
 * @returns {Object} 编辑配置
 */
export function createMachineEditConfig(item, idx, isAcceptance, isViewMode) {
  return {
    type: "machines",
    idx,
    title: "设备编辑",
    fields: [
      { key: "units", label: "单位", type: "unit", required: true },
      ...createCommonFields(isAcceptance, isViewMode),
    ],
    model: { ...item, originId: item.originId || item.id },
    unitList: MACHINE_UNIT_LIST,
    showDelete: item.originId ? false : true,
    info: {
      title: item.machineType || item.machineName,
    },
  };
}

<template>
  <view :class="['card', { active }]">
    <view class="child-image-section">
      <ImageSwiper
        class="child-swiper-box"
        :swiperList="
          childWorkOrderDetail.fileAttributes?.map((file, index) => ({ title: `作业图片${index + 1}`, url: file.path})) || []
        "
      />
    </view>
    <view class="child-detail-section">
      <view
        v-for="detailItem in detailInfoList"
        :key="detailItem.key"
        class="child-info-row"
      >
        <view
          class="child-info-label"
          :class="{ 'child-address-label': detailItem.key === 'address' }"
        >
          <template v-if="detailItem.key === 'address'">
            地
            <view class="child-address-spacing"></view>
            址：
          </template>
          <template v-else>
            {{ detailItem.label }}
          </template>
        </view>
        <view
          class="child-info-value"
          :class="{ 'child-address-link': detailItem.key === 'address' }"
          @click="detailItem.key === 'address' && handleAddressClick()"
        >
          <template v-if="detailItem.key === 'diseasesTypeName'">
            <view class="flex-row">
              <view>{{ detailItem.value }}</view>
              <view
                :class="[
                  'status-label',
                  getWorkStatusClass(childWorkOrderDetail?.workStatus),
                ]"
                >{{ childWorkOrderDetail.workStatusName }}</view
              >
            </view>
          </template>
          <template v-else-if="detailItem.key === 'address'">
            <view style="color: #4378ff">
              {{ detailItem.value || "-" }}
            </view>
          </template>
          <template v-else>
            {{ detailItem.value || "-" }}
          </template>
        </view>
      </view>
    </view>
    <view v-if="showReportBtn && hasPermission" class="card-report-btn-wrap">
      <button class="card-report-btn" @click="$emit('report')">作业上报</button>
    </view>
  </view>
</template>

<script setup>
import { computed, inject } from "vue";
import ImageSwiper from "../ImageSwiper.vue";
import { useUserStore } from "@/store/user";
const getWorkStatusClass = inject("getWorkStatusClass");

// 用户store
const userInfo = useUserStore();

const props = defineProps({
  childWorkOrderDetail: {
    type: Object,
    required: true,
  },
  active: {
    type: Boolean,
    default: false,
  },
  showReportBtn: {
    type: Boolean,
    default: false,
  },
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 维修配置信息
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
});

// 权限检查计算属性
const hasPermission = computed(() => {
  // 检查资源权限
  const hasResourcePermission =
    props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairWork") ||
    false;

  // 检查当前用户是否在工单用户列表中
  const currentUserId = userInfo.id;
  const isUserInWorkOrder =
    props.workOrderConfigDetail?.users?.some(
      (user) => user.relevancyId === currentUserId
    ) || false;

  return hasResourcePermission && isUserInWorkOrder;
});

const detailInfoList = computed(() => [
  {
    key: "diseasesTypeName",
    label: "病害类型：",
    value: `${props.childWorkOrderDetail.diseasesTypeName || '-'}(${props.childWorkOrderDetail.diseasesCount || '-'}${props.childWorkOrderDetail.diseasesUnit || ''})`,
  },
  {
    key: "remark",
    label: "事件说明：",
    value: props.childWorkOrderDetail.remark,
  },
  {
    key: "updownMarkName",
    label: "所属路段：",
    value: props.childWorkOrderDetail.updownMarkName,
  },
  {
    key: "eventObjectName",
    label: "事件对象：",
    value: props.childWorkOrderDetail.eventObjectName,
  },
  {
    key: "sectionName",
    label: "事件位置：",
    value: `${props.childWorkOrderDetail.stakeName || '-'}/${props.childWorkOrderDetail.endStakeName || '-'}(${props.childWorkOrderDetail.sectionName || '-'})`,
  },
  {
    key: "address",
    label: "地址：",
    value: props.childWorkOrderDetail.address,
  },
]);

const handleAddressClick = () => {
  const longitude = props.childWorkOrderDetail.longitude;
  const latitude = props.childWorkOrderDetail.latitude;
  const address = props.childWorkOrderDetail.address;
  if (!longitude || !latitude) {
    uni.showToast({ title: "暂无位置信息", icon: "none" });
    return;
  }
  uni.navigateTo({
    url: `/pages/common/viewLocation?isDetail=true&curLongitude=${String(longitude)}&curLatitude=${String(latitude)}&curAddress=${address}`,
  });
};
</script>

<style lang="scss" scoped>
@import "../../common.scss";

.flex-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card {
  width: 100%;
  border-radius: 24rpx;
  border: 2rpx solid transparent;
  background-color: #fff;
  box-sizing: border-box;
  padding: 28rpx 24rpx 32rpx 24rpx;
}

.card.active {
  border: 2rpx solid $primary-color;
}

.child-image-section {
  position: relative;
}

.child-detail-section {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.status-label {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 40rpx;
  font-family: $font-family;
  font-weight: 400;
  font-size: 24rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  margin-left: 8rpx;
}

.child-info-row {
  display: flex;
  line-height: 40rpx;
  .child-info-label {
    width: 140rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: $text-muted;
  }
  .child-info-value {
    flex: 1;
    font-weight: 400;
    font-size: 28rpx;
    color: $text-primary;
  }
}

.child-address-label {
  display: flex;
  .child-address-spacing {
    width: 56rpx;
  }
}

.child-address-link {
  color: $primary-color;
}

.card-report-btn-wrap {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.card-report-btn {
  width: 80%;
  height: 84rpx;
  line-height: 84rpx;
  background: $primary-color;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 40rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx 0 rgba(67, 120, 255, 0.1);
}
</style>

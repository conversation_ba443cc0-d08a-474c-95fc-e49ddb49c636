<template>
  <view class="work-order-confirm-container">
    <view class="work-order-confirm-content">
      <WorkOrderWrapCard :base-info="workOrderBaseInfo">
        <template #header>
          <view class="work-order-confirm-title">
            <view class="work-order-confirm-title-bar">
              <view class="title-indicator"></view>
              <view class="title-text">维修工单接受</view>
            </view>
          </view>
        </template>
        <template #body>
          <!-- 基础信息 -->
          <view
            v-for="item in baseInfoList"
            :key="item.key"
            class="work-order-info-row"
          >
            <view class="work-order-info-label">{{ item.label }}</view>
            <view class="work-order-info-value">
              {{ item.value || "-" }}
            </view>
          </view>
          <!-- 子工单详情 -->
          <view class="child-work-order-section">
            <ChildWorkOrderDetail
              :child-work-order-list="childWorkOrderList"
              :child-order-detail="childWorkOrderDetail"
              :current-page="currentChildOrderPage"
              @page-change="handleChildOrderPageChange"
            />
            <!-- 页码切换器 -->
            <view
              v-if="childWorkOrderList.length > 1"
              class="page-switcher-container"
            >
              <NumberSwitcher
                :current-page="currentChildOrderPage"
                :total-pages="childWorkOrderList.length"
                @change="handleChildOrderPageChange"
              />
            </view>
          </view>
        </template>
      </WorkOrderWrapCard>
    </view>
    <view class="footer-action-area" v-if="hasPermission">
      <button class="confirm-button" @click="handleConfirmOrder">
        确认工单
      </button>
    </view>
  </view>
</template>
<script setup>
import { ref, computed } from "vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import WorkOrderWrapCard from "../components/card/WorkOrderWrapCard.vue";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  // 子工单详情
  repairChildWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["getRepairChildWorkOrderDetail"]);

// 响应式数据
const currentChildOrderPage = ref(1);

// 计算属性
const workOrderBaseInfo = computed(() => props.repairWorkOrderBaseInfo || {});
const childWorkOrderList = computed(() => props.repairChildWorkOrderList || []);
const childWorkOrderDetail = computed(
  () => props.repairChildWorkOrderDetail || {}
);
const hasPermission = computed(() => {
  // 检查资源权限
  return (
    workOrderBaseInfo.value?.resourceList?.includes("dailyRepairConfirm") ||
    false
  );
});

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  return dateTime.replace(/T/, " ").substring(0, 16);
};

// 基础信息列表
const baseInfoList = computed(() => [
  {
    key: "createTime",
    label: "接收时间：",
    value: formatDateTime(workOrderBaseInfo.value?.createTime),
  },
  {
    key: "createUserName",
    label: "工单来源：",
    value: workOrderBaseInfo.value?.createUserName,
  },
]);

// 子工单页切换处理
const handleChildOrderPageChange = (page) => {
  currentChildOrderPage.value = page;
  // 请求子工单详情
  emit(
    "getRepairChildWorkOrderDetail",
    childWorkOrderList.value[page - 1]?.eventId
  );
};

// 确认工单处理
const handleConfirmOrder = () => {
  // 跳转工单计划计量
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/confirmOrder?workId=${workOrderBaseInfo.value.id}`,
  });
};
</script>
<style lang="scss">
@import "../common.scss";

.work-order-confirm-container {
  background-color: #f4f8ff;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .work-order-confirm-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    padding: 0 40rpx;
    box-sizing: border-box;
  }

  .work-order-confirm-title {
    margin-top: 28rpx;
    display: flex;
    align-items: center;

    .work-order-confirm-title-bar {
      display: flex;
      align-items: center;

      .title-indicator {
        margin-right: 8rpx;
        width: 6rpx;
        height: 28rpx;
        background: $primary-color;
        border-radius: 4rpx;
      }

      .title-text {
        font-family: $font-family;
        font-weight: 500;
        font-size: 32rpx;
        color: $text-secondary;
      }
    }
  }

  .work-order-info-row {
    display: flex;
    margin-top: 24rpx;

    .work-order-info-label {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-muted;
      line-height: 40rpx;
    }

    .work-order-info-value {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-primary;
      line-height: 40rpx;
    }
  }

  .child-work-order-section {
    padding: 40rpx 0;
  }

  .page-switcher-container {
    margin-top: 32rpx;
    padding: 0 28rpx;
  }

  .footer-action-area {
    height: 160rpx;
    margin-top: 24rpx;
    flex-shrink: 0;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent;

    .confirm-button {
      width: 92vw;
      height: 84rpx;
      line-height: 84rpx;
      border-radius: 8rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600;
      background: #4378ff;
      color: #ffffff;
      font-size: 40rpx;
      transition: all 0.2s ease;
      box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);

      &:active {
        background: #3366ee;
        transform: scale(0.99);
        transition: all 0.1s ease;
      }
    }
  }
}
</style>

import { computed, h } from "vue";
import { RoutineMaintenanceService } from "@/service";

export function useSelectPageConfig(projectStore, workOrderStore, pageType) {
  const configMap = computed(() => ({
    user: {
      title: "人员选择",
      unitLabel: "人",
      searchPlaceholder: "搜索人员姓名/部门",
      tagText: (item) => item.userName?.slice(-2) || "",
      filterFields: ["userName", "deptName"],
      tagMax: 6,
      tagShowPlus: true,
      async fetch() {
        const params = {
          page: "1",
          limit: "999999",
          projectId: projectStore.projectId,
        };
        const { data } =
          await RoutineMaintenanceService.getMaintainUserList(params);
        return data.map((item) => ({
          id: item.id,
          userName: item.userName,
          deptName: item.deptName,
        }));
      },
      render: (item) => h('view', {}, [
        h('view', { class: 'select-card-avatar' }, item.userName?.slice(-2)),
        h('view', { class: 'select-card-info' }, [
          h('view', { class: 'select-card-title' }, item.userName),
          h('view', { class: 'select-card-desc' }, item.deptName)
        ])
      ]),
    },
    work: {
      title: "作业选择",
      unitLabel: "个作业内容",
      searchPlaceholder: "搜索作业名称/描述",
      tagText: (item) => `【${item.code}】${item.name}` || "",
      filterFields: ["code", "name"],
      tagMax: 1,
      tagShowPlus: true,
      async fetch() {
        const params = { page: "1", limit: "999999" };
        const { data } =
          await RoutineMaintenanceService.getMaintainWorkList(params);
        return data || [];
      },
      render: (item) => h('view', { class: 'work-item-wrap' }, [
        h('view', { class: 'select-card-title' }, `【${item.code}】${item.name}`),
        h('view', { class: 'select-card-extra' }, `¥${item.unitPrice}/${item.unitName}`)
      ]),
    },
    device: {
      title: "设备选择",
      unitLabel: "个设备",
      searchPlaceholder: "搜索设备名称",
      tagText: (item) => item.name || "",
      filterFields: ["name", "administrator", "phone"],
      tagMax: 1,
      tagShowPlus: false,
      async fetch() {
        const params = {
          page: "1",
          limit: "999999",
          depId: workOrderStore?.departmentId,
          status: "1",
        };
        const { data } =
          await RoutineMaintenanceService.getMachineInformationList(params);
        return data.map((item) => ({
          id: item.id,
          name: item.name,
          phone: item.phone,
          administrator: item.administrator,
        }));
      },
      render: (item) => h('view', { class: 'device-item-wrap' }, [
        h('view', { class: 'select-card-title' }, item.name),
        h('view', { class: 'select-card-desc' }, `${item.administrator}-${item.phone}`)
      ]),
    },
    material: {
      title: "材料选择",
      unitLabel: "个材料",
      searchPlaceholder: "搜索材料名称",
      tagText: (item) => item.typeName || "",
      filterFields: ["typeName", "specification", "unit", "inventory"],
      tagMax: 1,
      tagShowPlus: false,
      async fetch() {
        const { data: warehouseList } =
          await RoutineMaintenanceService.getWarehouseList({
            projectId: projectStore.projectId,
          });
        const params = {
          page: "1",
          limit: "999999",
          projectId: projectStore.projectId,
          warehouseId: warehouseList[0] && warehouseList[0]?.id,
          inventoryNotZero: true,
        };
        const { data } =
          await RoutineMaintenanceService.getMaterialInformationList(params);
        return data;
      },
      render: (item) => h('view', { class: 'material-item-wrap' }, [
        h('view', { class: 'select-card-title' }, item.typeName),
        h('view', { class: 'select-card-desc' }, `${item.specification}/${item.unit}`),
        h('view', { class: 'select-card-extra' }, `剩余${item.inventory}${item.unit}`)
      ]),
    },
  }));
  const config = computed(
    () =>
      configMap.value[pageType.value] || {
        render: () => ({ title: "", desc: "", extra: "" }),
      }
  );

  return { config };
}


<template>
  <view>
    <uv-popup
      ref="popup"
      mode="center"
      round="8"
      @change="change"
      :customStyle="customStyle"
    >
      <view class="box">
        <view class="title">确定开始日常维修？</view>
        <view class="info">请确保施工用料/方案无误后再开始施工</view>
        <view class="check_btn">
          <view @click="onStart">
            <view class="btn_text">开始施工</view>
            <view class="btn_time">{{curTime}}</view>
          </view>
        </view>
        <view class="cur_location">
          <view class="location">当前定位：{{curAddress}}</view>
          <uv-icon name="reload" :class="[isRefreshLoading?'refresh_anim':'']" color="#4378FF" size="14" @click="onRefreshLocation"></uv-icon>
        </view>
        <!-- <view class="add_remark_btn" @click="onRemark">添加备注</view> -->
        <view class="add_remark_btn">
          <view class="remark_notice_text">
            <view style="color: #A09F9F;">备注：</view>
            <view class="remark" @click="onRemark">{{curRemark||'如有特殊说明请填写备注'}}</view>
          </view>
          <uv-icon class="arrow_right" name="arrow-right" size="12" @click="onRemark"></uv-icon>
        </view>
        <view class="btn_box">
          <uv-button
            :disabled="isCancelDisabled"
            :custom-style="cancelBtnStyle"
            :customTextStyle="btnTextStyle"
            text="取消"
            @click="onCancel"
          ></uv-button>
        </view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
const props = defineProps({
  curTime:{
    type: String,
    required: true,
    default:'08:00'
  },
  curAddress:{
    type: String,
    required: true,
    default:'-'
  },
  curRemark:{
    type: String,
    default:''
  },
  isCancelDisabled:{
    type: Boolean,
    required: true,
    default:false
  },
  isRefreshLoading:{
    type: Boolean,
    required: true,
    default:false
  }
})

const btnTextStyle = {
  fontSize: "32rpx",
};
const cancelBtnStyle = {
  marginBottom: "28rpx",
  width: "520rpx",
  height: "72rpx",
  padding: "14rpx 0",
  lineHeight: "44rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#e4e4e4",
  color: "#373737",
};

const customStyle = reactive({
  width: "608rpx",
  padding: "48rpx 44rpx",
  boxSizing: "border-box",
});

const emit = defineEmits(["onBuildCallback"]);
// 开始施工
const onStart = () => {
  emit("onBuildCallback", { type: "onStart" });
};
// 刷新定位
const onRefreshLocation = () => {
  emit("onBuildCallback", { type: "onRefreshLocation" });
};
// 取消
const onRemark = () => {
  emit("onBuildCallback", { type: "onRemark" });
};
// 取消
const onCancel = () => {
  emit("onBuildCallback", { type: "onCancel" });
};
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
};

defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-sizing: border-box;
  .title{
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 36rpx;
    color: #373737;
    line-height: 58rpx;
  }
  .info {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #A09F9F;
    line-height: 48rpx;
    margin-top: 20rpx;
  }
  .check_btn{
    margin-top: 58rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 244rpx;
    height: 244rpx;
    border: 10rpx solid #4378FF;
    border-radius: 50%;
    .btn_text, .btn_time{
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 40rpx;
      color: #404040;
      line-height: 56rpx;
    }
    .btn_time{
      margin-top: 12rpx;
    }
  }
  .cur_location{
    display: flex;
    align-content:center;
    margin-top: 38rpx;
    .location{
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #A09F9F;
      line-height: 48rpx;
      margin-right: 6rpx;
      display: -webkit-box;  
      -webkit-box-orient: vertical;  
      -webkit-line-clamp: 2; /* 限定文本行数 */  
      overflow: hidden;  
      line-height: 1.5; /* 根据你的需要调整 */  
      max-height: calc(1.5 * 2 * 1em); /* 假设你的字体大小为1em，行高为1.5，这里计算两行的最大高度 */  
      text-overflow: ellipsis; /* 显示省略号 */  
    }
    .refresh_anim{
      transform: rotate(360deg);
      transition: all 1s;
    }
  }
  .add_remark_btn{
    display: flex;
    align-items: center;
    margin-top: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 48rpx;
    .remark_notice_text{
      display: flex;
      align-items: center;
    }
    .remark{
      width: 308rpx;
      color: #D9D9D9;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .arrow_right{
      margin-left: 16rpx;
    }
  }
  .btn_box {
    margin-top: 48rpx;
    .btn {
      width: 520rpx;
      height: 72rpx;
      border-radius: 8rpx;
      padding: 14rpx 0;
      font-weight: 600;
      font-size: 32rpx;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 28rpx;
      box-sizing: border-box;
    }
    .cancel {
      background: #e4e4e4;
      color: #373737;
    }
  }
}
</style>

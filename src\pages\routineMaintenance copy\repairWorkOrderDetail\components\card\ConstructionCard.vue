<template>
  <WorkOrderWrapCard :baseInfo="baseInfo" :isShowBar="isShowBar">
    <template #header>
      <view class="flex-row-between">
        <view class="flex-row-between">
          <view class="blue-bar"></view>
          <view class="bar-text">任务详情</view>
        </view>
      </view>
      <view class="message_notice">{{
        detailInfo?.planContent?.remark || "尽快维修，恢复道路通车！"
      }}</view>
    </template>
    <template #body v-if="!pageLoading">
      <!-- 维修对象 -->
      <view class="info_box_wrap">
        <view class="info_box">
          <view class="left_title">维修对象：</view>
          <view class="right_excel">
            <view class="table_header flex-row-between">
              <view class="table_item_text">病害类型</view>
              <view class="table_item_text">病害对象</view>
            </view>
            <view
              class="table_body flex-row-between"
              v-for="(childOrderItem, childIndex) in childOrderList"
              :key="childIndex"
            >
              <view class="table_item_text">{{
                childOrderItem.diseasesTypeName
              }}</view>
              <view class="table_item_text">{{
                childOrderItem.eventObjectName
              }}</view>
            </view>
          </view>
        </view>
        <!-- 维修人员 -->
        <view class="info_box">
          <view class="left_title">维修人员：</view>
          <view class="right_excel">
            <view class="table_header flex-row-between">
              <view class="table_item_text">人员名称</view>
              <view class="table_item_text">计划施工天数</view>
            </view>
            <view
              class="table_body flex-row-between"
              v-for="(userItem, userIndex) in configDetail.users"
              :key="userIndex"
            >
              <view class="table_item_text">{{
                userItem.userName || "--"
              }}</view>
              <view class="table_item_text">{{
                userItem.planNum || "--"
              }}</view>
            </view>
          </view>
        </view>
        <!-- 使用材料 -->
        <view class="info_box" v-if="configDetail?.materials?.length">
          <view class="left_title">使用材料：</view>
          <view class="right_excel">
            <view class="table_header flex-row-between">
              <view class="table_item_text">材料名称</view>
              <view class="table_item_text">计划使用数量</view>
            </view>
            <view
              class="table_body flex-row-between"
              v-for="(materialItem, materialIndex) in configDetail.materials"
              :key="materialIndex"
            >
              <view class="table_item_text">{{
                materialItem.name || "--"
              }}</view>
              <view class="table_item_text">{{
                materialItem.planNum || "--"
              }}</view>
            </view>
          </view>
        </view>
        <!-- 使用机械 -->
        <view class="info_box" v-if="configDetail?.machines?.length">
          <view class="left_title">使用机械：</view>
          <view class="right_excel">
            <view class="table_header flex-row-between">
              <view class="table_item_text">机械名称</view>
              <view class="table_item_text">计划使用天数</view>
            </view>
            <view
              class="table_body flex-row-between"
              v-for="(machineItem, machineIndex) in configDetail.machines"
              :key="machineIndex"
            >
              <view class="table_item_text">{{
                machineItem.name || "--"
              }}</view>
              <view class="table_item_text">{{
                machineItem.planNum || "--"
              }}</view>
            </view>
          </view>
        </view>
        <!-- 派工数量 -->
        <view class="info_box" v-if="configDetail?.settlements?.length">
          <view class="left_title">派工数量：</view>
          <view
            class="info_box_message_notice"
            v-if="configDetail.settlements.some((item) => item.changed === '1')"
          >
            <image
              src="@/static/icon/err_icon20250115.png"
              alt=""
            ></image>
            <view>标红作业内容发生变更，请与负责人确认！</view>
          </view>
          <view class="right_excel">
            <view class="table_header flex-row-between">
              <view class="table_item_text">作业内容</view>
              <view class="table_item_text">计划作业数量</view>
            </view>
            <view
              :class="[
                'table_body',
                'flex-row-between',
                settlementItem.changed === '1' ? 'notice_active' : '',
              ]"
              v-for="(
                settlementItem, settlementIndex
              ) in configDetail.settlements"
              :key="settlementIndex"
            >
              <view class="table_item_text">{{
                settlementItem.content || "--"
              }}</view>
              <view class="table_item_text"
                >{{ settlementItem.planNumber || "--"
                }}{{
                  settlementItem.planNumber && settlementItem.unitName
                }}</view
              >
            </view>
          </view>
        </view>
        <!-- 维修方案 -->
        <view class="maintenance_plan" v-if="detailInfo?.planContent?.filePath">
          <view class="left_title">维修方案：</view>
          <uv-loading-icon
            :show="downloadLoading"
            size="26rpx"
          ></uv-loading-icon>
          <view
            class="right_file_name"
            @click="toViewFile(detailInfo?.planContent)"
            >{{ detailInfo?.planContent?.filePath?.split('/').pop() || '附件' }}</view
          >
        </view>
      </view>
    </template>
  </WorkOrderWrapCard>
</template>
<script setup>
import { viewFile, getDownloadLoading } from "@/utils/fileViewer";
import WorkOrderWrapCard from "./WorkOrderWrapCard";

const props = defineProps({
  baseInfo: Object,
  detailInfo: Object,
  childOrderList: Array,
  configDetail: Object,
  isShowBar: Boolean,
});
const emit = defineEmits(["handleStart"]);

// 使用工具函数的下载状态
const downloadLoading = getDownloadLoading();

// 使用工具函数查看文件（默认使用 showImg 处理 URL）
const toViewFile = viewFile;
</script>
<style lang="scss" scoped>
@import "../../common.scss";

.message_notice {
  margin-top: 28rpx;
  background: #fee6e4;
  border-radius: 8rpx;
  font-weight: 400;
  padding: 12rpx;
  font-size: 24rpx;
  color: #ff3838;
}

.info_box_wrap {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding-top: 24rpx;
  padding-bottom: 40rpx;
}

.info_box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  display: flex;
  .left_title {
    font-weight: 400;
    font-size: 28rpx;
    color: #b0b0b0;
    line-height: 40rpx;
  }
  .info_box_message_notice {
    margin-top: 8rpx;
    display: flex;
    align-items: center;
    width: 100%;
    background: rgba(249, 213, 217, 1);
    padding: 6rpx 18rpx;
    border-radius: 8rpx;
    image {
      width: 18px;
      height: 18px;
    }
    view {
      margin-left: 7px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #ff2e00;
      line-height: 40rpx;
    }
  }
  .right_content {
    font-weight: 400;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
  .right_excel {
    margin-top: 20rpx;
    width: 100%;
    border-radius: 4rpx;
    border: 2rpx solid #f2f2f2;
    .table_header {
      height: 50%;
      line-height: 48rpx;
      background-color: rgba(242, 242, 242, 1);
      font-weight: 400;
      font-size: 28rpx;
      color: #636363;
    }
    .table_body {
      height: 50%;
      line-height: 48rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      border-bottom: 2rpx solid #f2f2f2;
    }
    .table_body:last-child {
      border-bottom: none;
    }
    .notice_active {
      color: #ff2e00;
    }
    .table_item_text {
      width: 50%;
      text-align: center;
    }
  }
}
.maintenance_plan {
  display: flex;
  align-items: center;
  .left_title {
    width: 160rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
    white-space: nowrap;
  }
  .uv-loading-icon {
    margin-right: 8rpx;
  }
  .right_file_name {
    width: 450rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #1890ff;
    line-height: 40rpx;
    word-wrap: break-word;
  }
}

.skeleton {
  margin-top: 28rpx;
}
</style>

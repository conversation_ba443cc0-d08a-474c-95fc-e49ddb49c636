<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="日常维修"
      leftIconColor="#fff"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="true"
      bgColor="#065BFF"
    >
      <template v-slot:right>
        <view class="uv-nav-slot" @click="toHistoryPage">历史工单</view>
      </template>
    </uv-navbar>
    <!-- 页面内容 -->
    <view class="content">
      <!-- 顶部日历 -->
      <view class="calendar_box">
        <zjy-calendar
          class="calendar uni-calendar--hook"
          :showMonth="false"
          :nextMonth="calendarInfo.nextMonth"
          :endDate="calendarInfo.endDate"
          :calendarTop="calendarTop"
          @watchWeeks="watchWeeks"
          @change="changeDate"
          @monthSwitch="monthSwitch"
        />
        <view class="touch_bar" @click="toggleCalendar">
          <image
            v-if="!isCalendarExpanded"
            class="down_img"
            src="../../static/icon/touch_down_icon.png"
          ></image>
          <image
            v-else
            class="up_img"
            src="../../static/icon/touch_up_icon.png"
          ></image>
        </view>
      </view>
      <view class="todo_container">
        <view class="todo_title">
          <view class="todo_title_bar"></view>
          <view class="todo_title_text">
            {{
              isToday
                ? `我的待办·${dayjs(choosedDate).format("YYYY年MM月DD日")}`
                : `已完成任务·${dayjs(choosedDate).format("YYYY年MM月DD日")}`
            }}
          </view>
        </view>
        <view class="tabs-container">
          <view class="tabs-box1">
            <view
              :class="['tab', activeTab === item.value ? 'tab-active' : '']"
              v-for="(item, index) in toDoTypes"
              :key="index"
              @click="changeTab(item.value)"
            >
              {{ item.label }}·{{ item.num }}
            </view>
          </view>
        </view>
        <!-- 日常维修 -->
        <view v-if="activeTab === '1'">
          <DailyRepairCard
            v-if="todoMap.workOrderMobileTodayVO.length"
            :dataList="todoMap.workOrderMobileTodayVO"
          />
          <NoData v-else />
        </view>
        <!-- 维修确认、维修验收 -->
        <view v-if="activeTab === '2'">
          <DailyRepairCard
            v-if="todoMap.workOrderMobileApprove.length"
            :dataList="todoMap.workOrderMobileApprove"
          />
          <NoData v-else />
        </view>
        <view style="height: 200rpx"></view>
      </view>
      <uv-loading-icon
        :show="pageLoading"
        text="加载中..."
        textSize="30rpx"
      ></uv-loading-icon>
    </view>
    <!-- 底部切换按钮 -->
    <ylg-my-btns
      :switchActive="switchActive"
      @myBtnCallback="myBtnCallback"
    ></ylg-my-btns>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import { onShow, onReady, onPullDownRefresh } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import zjyCalendar from "@/uni_modules/zjy-calendar/components/zjy-calendar/zjy-calendar.vue";
import { homepageApi } from "@/service";
import { useProjectStore } from "@/store/project";
import DailyRepairCard from "@/pages/home/<USER>/DailyRepairCard.vue";
import NoData from "@/components/ylg-nodata.vue";

const projectInfo = useProjectStore();
const statisticData = reactive({});
const todoMap = reactive({
  workOrderMobileTodayVO: [], // 日常维修
  workOrderMobileApprove: [], // 日常维修-维修确认、维修验收
});
const activeTab = ref("1");
const toDoTypes = computed(() => {
  return [
    {
      value: "1",
      label: "作业单",
      num: statisticData.taskNum,
    },
    {
      value: "2",
      label: "审批单",
      num: statisticData.approveNum,
    },
  ];
});
const changeTab = (value) => {
  if (value === activeTab.value) return;
  activeTab.value = value;
  getAllData();
};

// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 86
}rpx`;

const pageLoading = ref(false);
onShow(() => {
  switchActive.value = false;
  getAllData();
});
onReady(() => {
  calendarTop.value = isCalendarExpanded.value
    ? "0rpx"
    : `${Number(curItemIndex.value) * -80}rpx`;
});
onPullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  await getAllData();
  uni.stopPullDownRefresh();
});
// 日历相关
const calendarConentHeight = ref("270rpx"); // 初始高度
const isCalendarExpanded = ref(false);
const choosedDate = ref(dayjs().format("YYYY-MM-DD"));
const isToday = ref(true);

const calendarTop = ref("0rpx");
const toggleCalendar = () => {
  isCalendarExpanded.value = !isCalendarExpanded.value;
  calendarConentHeight.value = isCalendarExpanded.value ? "588rpx" : "270rpx";
  // 设置收起日历时露出当前选中日期所在行
  calendarTop.value = isCalendarExpanded.value
    ? "0rpx"
    : `${Number(curItemIndex.value) * -80}rpx`;
};

const getDate = (date, AddDayCount = 0) => {
  if (!date) {
    date = new Date();
  }
  if (typeof date !== "object") {
    date = date.replace(/-/g, "/");
  }
  const dd = new Date(date);
  dd.setDate(dd.getDate() + AddDayCount); // 获取AddDayCount天后的日期
  const y = dd.getFullYear();
  const m =
    dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1; // 获取当前月份的日期，不足10补0
  const d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate(); // 获取当前几号，不足10补0
  return {
    fullDate: y + "-" + m + "-" + d,
    year: y,
    month: m,
    date: d,
    day: dd.getDay(),
  };
};
const calendarInfo = ref({
  lunar: true,
  range: false,
  insert: false,
  nextMonth: false,
  endDate: getDate(new Date(), 0).fullDate,
});
const monthSwitch = (e) => {
  console.log("monthSwitch", e);
  if (Number(e.month) === Number(dayjs().month()) + 1) {
    calendarInfo.value.nextMonth = false;
  } else {
    calendarInfo.value.nextMonth = true;
  }
};
const curItemIndex = ref(0);
const watchWeeks = (e) => {
  console.log("切换周", e.curItemIndex);
  curItemIndex.value = e.curItemIndex;
};
const changeDate = (e) => {
  // 设置收起日历时露出当前选中日期所在行
  curItemIndex.value = e.curItemIndex;
  choosedDate.value = e.fulldate;
  isToday.value = !!(getDate().fullDate == e.fulldate);
  getAllData();
};

const getAllData = async () => {
  try {
    pageLoading.value = true;
    const { code, data } = await homepageApi.yhWorkOrderMobileTodayTodoList({
      projectId: projectInfo.projectId,
      toDoType: activeTab.value,
      date: choosedDate.value,
    });
    console.log("今日列表", code, data);
    if (code == 200) {
      if (activeTab.value == "1") {
        todoMap.workOrderMobileTodayVO = (data.toDo && data?.toDo) || [];
      } else if (activeTab.value == "2") {
        todoMap.workOrderMobileApprove = (data.toDo && data?.toDo) || [];
      }
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
    console.log("请求今日列表失败", error, pageLoading.value);
  }
  try {
    const { code, data } = await homepageApi.yhWorkOrderMobileTodayTodoCount({
      projectId: projectInfo.projectId,
      date: choosedDate.value,
    });
    if (code == 200) {
      statisticData.taskNum = data.taskNum;
      statisticData.approveNum = data.approveNum;
    }
  } catch (error) {
    console.log(error);
  }
};

// todo  注意驳回的工单
// const handleCardClick = (envRes) => {
//   console.log("卡片回调", envRes);
//   // 驳回的工单
//   if (envRes.workResult == "0") {
//     uni.navigateTo({
//       url: `/pages/routineMaintenance/repairWorkOrderDetail/index?workId=${envRes.id}`,
//     });
//   } else {
//     switch (envRes.workStatus) {
//       // "待施工",
//       case "2":
//         uni.navigateTo({
//           url: `/pages/routineMaintenance/repairWorkOrderDetail/_2StartConstruction?id=${envRes.id}&workCode=${envRes.workCode}&workStatus=${envRes.workStatus}`,
//         });
//         break;
//       // "施工中"
//       case "3":
//         uni.navigateTo({
//           url: `/pages/routineMaintenance/repairWorkOrderDetail/_3UnderConstruction?id=${envRes.id}&workCode=${envRes.workCode}&workStatus=${envRes.workStatus}&workResult=${envRes.workResult}`,
//         });
//         break;
//       // "待核验"
//       case "4":
//         uni.navigateTo({
//           url: `/pages/routineMaintenance/repairWorkOrderDetail/_4Verification?workId=${envRes.id}`,
//         });
//         break;
//       // "已完成"
//       case "5":
//         uni.navigateTo({
//           url: `/pages/routineMaintenance/repairWorkOrderDetail/_5Completed?workId=${envRes.id}`,
//         });
//         break;

//       default:
//         break;
//     }
//   }
// };

// 查看历史记录
const toHistoryPage = () => {
  uni.navigateTo({
    url: "/pages/routineMaintenance/historyMaintenance",
  });
};
const switchActive = ref(false);
const myBtnCallback = (envRes) => {
  switch (envRes.type) {
    case "toggoleSwitch":
      switchActive.value = !switchActive.value;
      break;
    case "toReportEvent":
      uni.navigateTo({
        url: `/pages/routineMaintenance/repairWorkOrderDetail/index`,
      });
      break;
    default:
      break;
  }
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background-color: #fefefe;
}
.uv-loading-icon {
  margin-top: 168rpx;
}
.uv-nav-slot {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.calendar_box {
  position: relative;
  height: v-bind(calendarConentHeight);
  overflow: hidden;
  transition: height 0.8s ease;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(0, 0, 0, 0.08);
  .calendar {
    :deep(.uni-calendar__backtoday) {
      display: none;
    }
    :deep(.uni-calendar__header) {
      height: 84rpx;
      border-bottom: none;
    }
    :deep(.uni-calendar__header-text) {
      font-weight: 600;
      font-size: 36rpx;
      color: #404040;
      line-height: 50rpx;
    }
    // 周描述
    :deep(.uni-calendar__weeks-day) {
      height: 84rpx;
      border-bottom: 2rpx solid #f0f0f0;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #606060;
      line-height: 44rpx;
    }
    // 日期 item
    :deep(.uni-calendar__weeks-item) {
      height: 80rpx;
      width: 107rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    :deep(.uni-calendar-item__weeks-box) {
      flex: none;
      text-align: center;
      font-size: 32rpx;
      line-height: 44rpx;
      color: #404040;
    }
    :deep(.uni-calendar-item--isDay-text:nth-child(2)) {
      display: none;
    }
    :deep(.uni-calendar-item__weeks-box-circle) {
      position: absolute;
      top: 80rpx;
      left: 46rpx;
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
    }
    :deep(.uni-calendar-item--disable) {
      font-size: 32rpx;
      color: #a09f9f;
      line-height: 44rpx;
    }
    :deep(.uni-calendar-item--isDay),
    :deep(.uni-calendar-item--checked) {
      border-radius: 50%;
      width: 60rpx;
      height: 60rpx;
      font-weight: 600;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 60rpx;
      background: #4378ff;
      opacity: 1;
    }
  }
  .touch_bar {
    width: 100%;
    height: 40rpx;
    // background: rgb(182, 140, 140);
    position: absolute;
    bottom: 0rpx;
    .down_img {
      position: absolute;
      bottom: 16rpx;
      left: 50%;
      transform: translateX(-40rpx);
      display: block;
      width: 80rpx;
      height: 8rpx;
    }
    .up_img {
      position: absolute;
      bottom: 6rpx;
      left: 50%;
      transform: translateX(-26rpx);
      display: block;
      width: 52rpx;
      height: 24rpx;
    }
  }
}
.todo_container {
  padding: 28rpx;
  .todo_title {
    display: flex;
    align-items: center;
    gap: 0 12rpx;
    height: 52rpx;
    margin-bottom: 28rpx;
    .todo_title_bar {
      width: 8rpx;
      height: 32rpx;
      background: #3c62fa;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
    }
    .todo_title_text {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 36rpx;
      color: #373737;
    }
  }
  .tabs-container {
    .tabs-box1 {
      display: flex;
      align-items: center;
      gap: 0 64rpx;
      margin-bottom: 28rpx;
      .tab {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #404040;
      }
      .tab-active {
        font-weight: bold;
        background: linear-gradient(
            to right,
            rgba(51, 109, 255, 1),
            rgba(124, 161, 255, 0.8),
            rgba(67, 120, 255, 0.1),
            rgba(67, 120, 255, 0.05)
          )
          no-repeat bottom / 100% 8rpx;
      }
    }
  }
}
</style>

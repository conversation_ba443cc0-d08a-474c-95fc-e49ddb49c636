<template>
  <view class="work-order-detail-container">
    <!-- 工单确认环节 -->
    <i_1Confirm
      v-if="workOrderBaseInfo.workStatus == 1"
      :repairWorkOrderBaseInfo="workOrderBaseInfo"
      :repairChildWorkOrderList="childWorkOrderList"
      :repairChildWorkOrderDetail="childWorkOrderDetail"
      @getRepairChildWorkOrderDetail="getChildWorkOrderDetail"
    />
    <!-- 开始施工环节 -->
    <i_2StartConstruction
      v-if="workOrderBaseInfo.workStatus == 2"
      :repairWorkOrderBaseInfo="workOrderBaseInfo"
      :repairWorkOrderDetail="workOrderDetail"
      :repairChildWorkOrderList="childWorkOrderList"
      :workOrderConfigDetail="workOrderConfigDetail"
    />
    <!-- 施工中环节 -->
    <i_3UnderConstruction
      v-if="workOrderBaseInfo.workStatus == 3"
      :repairWorkOrderBaseInfo="workOrderBaseInfo"
      :repairWorkOrderDetail="workOrderDetail"
      :childWorkOrderCompleteInfo="childWorkOrderCompleteInfo"
      :repairChildWorkOrderList="childWorkOrderList"
      :repairChildWorkOrderDetail="childWorkOrderDetail"
      :workOrderConfigDetail="workOrderConfigDetail"
      @getRepairChildWorkOrderDetail="getChildWorkOrderDetail"
    />
    <!-- 验收环节 -->
    <i_4Verification
      v-if="workOrderBaseInfo.workStatus == 4"
      :repairWorkOrderBaseInfo="workOrderBaseInfo"
      :repairWorkOrderDetail="workOrderDetail"
      :repairChildWorkOrderList="childWorkOrderList"
      :workOrderConfigDetail="workOrderConfigDetail"
      :childWorkOrderCompleteInfo="childWorkOrderCompleteInfo"
    />
    <!-- 完工环节 -->
    <i_5Completed
      v-if="workOrderBaseInfo.workStatus == 5"
      :repairWorkOrderBaseInfo="workOrderBaseInfo"
      :repairWorkOrderDetail="workOrderDetail"
      :childWorkOrderCompleteInfo="childWorkOrderCompleteInfo"
      :repairChildWorkOrderList="childWorkOrderList"
      :workOrderConfigDetail="workOrderConfigDetail"
    />
  </view>
  <uv-skeleton class="skeleton" rows="12" :loading="pageLoading"></uv-skeleton>
</template>

<script setup>
import { ref, watch, provide, onMounted, onUnmounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import i_1Confirm from "./modules/i_1Confirm.vue";
import i_2StartConstruction from "./modules/i_2StartConstruction.vue";
import i_3UnderConstruction from "./modules/i_3UnderConstruction.vue";
import i_4Verification from "./modules/i_4Verification.vue";
import i_5Completed from "./modules/i_5Completed.vue";
import { RoutineMaintenanceService } from "@/service";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";
// Store实例
const workOrderStore = useRepairWorkOrderStore();

// 获取工单基础信息
const workOrderId = ref("");
const workOrderBaseInfo = ref({});
const getWorkOrderBaseInfo = async () => {
  const { data } = await RoutineMaintenanceService.workOrderBaseInfo(
    workOrderId.value
  );
  workOrderStore.updateBaseInfo(data);
  workOrderBaseInfo.value = data;
};

// 获取工单详细信息
const workOrderDetail = ref({});
const getWorkOrderDetail = async (id) => {
  const { data } = await RoutineMaintenanceService.workOrderDetail(id);
  workOrderDetail.value = data;
};

// 监听工单状态变化，动态请求相关接口
watch(
  () => workOrderBaseInfo.value.workStatus,
  async (status) => {
    const id = workOrderBaseInfo.value?.id;
    pageLoading.value = true;
    switch (status) {
      case "1":
        // 待确认
        await getChildOrderDetailList(id);
        // 获取第一个(当前显示的详情)
        await getChildWorkOrderDetail(childWorkOrderList.value[0]?.eventId);
        break;
      case "2":
        // 待施工
        await getChildOrderDetailList(id);
        await getWorkOrderConfigDetail(id);
        await getWorkOrderDetail(id);
        break;
      case "3":
        // 施工中
        await getChildOrderDetailList(id);
        await getWorkOrderConfigDetail(id);
        await getWorkOrderDetail(id);
        await getChildWorkOrderCompleteInfo(id);
        // 获取第一个(当前显示的详情)
        await getChildWorkOrderDetail(childWorkOrderList.value[0]?.eventId);
        break;
      case "4":
        // 待核验
        await getWorkOrderDetail(id);
        await getChildOrderDetailList(id);
        await getWorkOrderConfigDetail(id);
        await getChildWorkOrderCompleteInfo(id);
        break;
      case "5":
        // 已完成
        await getChildOrderDetailList(id);
        await getWorkOrderConfigDetail(id);
        await getWorkOrderDetail(id);
        await getChildWorkOrderCompleteInfo(id);
        break;
    }
    pageLoading.value = false;
  }
);

const pageLoading = ref(false);

// 获取子工单列表
const childWorkOrderList = ref([]);
const getChildOrderDetailList = async (id) => {
  const { data } = await RoutineMaintenanceService.getChildOrder(id);
  childWorkOrderList.value = data;
};

// 获取子工单详情
const childWorkOrderDetail = ref({});
const getChildWorkOrderDetail = async (eventId) => {
  const { data } =
    await RoutineMaintenanceService.getInspectEventDetail(eventId);
  childWorkOrderDetail.value = data;
};

// 子工单获取维修配置
const workOrderConfigDetail = ref({});
const getWorkOrderConfigDetail = async (id) => {
  const { data } = await RoutineMaintenanceService.getCostInfo(id);
  workOrderConfigDetail.value = data;
};

// 子工单任务完成情况
const childWorkOrderCompleteInfo = ref({});
const getChildWorkOrderCompleteInfo = async (id) => {
  const { data } = await RoutineMaintenanceService.workOrderCompleteInfo(id);
  childWorkOrderCompleteInfo.value = data;
};

// 工单状态映射
const WORK_STATUS_MAP = {
  1: { name: "待确认", class: "pending" },
  2: { name: "待施工", class: "to-do" },
  3: { name: "施工中", class: "in-progress" },
  4: { name: "待核验", class: "verify" },
  5: { name: "已完成", class: "completed" },
};
// 获取工单状态样式类
const getWorkStatusClass = (status) => WORK_STATUS_MAP[status]?.class;

// 提供给子组件
provide("getWorkStatusClass", getWorkStatusClass);

// 组件挂载时添加事件监听
onMounted(() => {
  uni.$on("refreshWorkOrderBaseInfo", getWorkOrderBaseInfo);
  uni.$on("refreshChildOrderDetailList", getChildOrderDetailList);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off("refreshWorkOrderBaseInfo", getWorkOrderBaseInfo);
  uni.$off("refreshChildOrderDetailList", getChildOrderDetailList);
});

onLoad((options) => {
  if (options.id) {
    workOrderId.value = options.id;
    getWorkOrderBaseInfo();
  }
});
</script>
<style lang="scss" scoped>
.work-order-detail-container {
  height: 100vh;
  overflow: hidden;
}
</style>

<template>
  <view class="review-card">
    <view class="card-title-row">
      <view class="blue-bar"></view>
      <text class="bar-text">审核记录</text>
    </view>
    <view class="check_logs">
      <view
        class="log"
        v-for="(logItem, logIndex) in checkLogList"
        :key="logIndex"
      >
        <view class="log_title">
          <view class="title_left_wrap">
            <image class="title_icon" :src="checkIcons[logItem.result]"></image>
            <view class="title_text">{{ logItem.resultName }}</view>
          </view>
          <view
            v-if="logItem.result === '2' && hasPermission"
            class="title_right_text"
            @click="emit('goAcceptanceMeasurement', logIndex)"
            >计量详情</view
          >
        </view>
        <view class="review-reason" v-if="!Number(logItem.result)"
          >原因：{{ logItem.remark }}</view
        >
        <view class="review-meta">
          <text class="meta-item"
            >审核人：<text class="meta-item-value">{{
              logItem.createUserName
            }}</text></text
          >
          <text class="meta-item"
            >审核时间：<text class="meta-item-value">{{
              logItem.createTime
            }}</text></text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from "vue";
const props = defineProps({
  baseInfo: {
    type: Object,
    default: () => {},
  },
  checkLogList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["goAcceptanceMeasurement"]);

const checkIcons = {
  0: "/static/icon/check_reject_20240827.png",
  2: "/static/icon/check_pass_20240827.png",
};

const hasPermission = computed(() => {
  // 检查资源权限
  return (
    props.baseInfo?.resourceList?.includes("dailyRepairVerification") ||
    false
  );
});
</script>

<style lang="scss" scoped>
@import "../../common.scss";

.review-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(67, 120, 255, 0.08);
  padding: 24rpx 24rpx 32rpx;
}
.card-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.check_logs {
  .log {
    margin-top: 32rpx;
    .log_title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title_left_wrap {
        display: flex;
        align-items: center;
        .title_icon {
          display: block;
          width: 30rpx;
          height: 30rpx;
        }
        .title_text {
          margin-left: 22rpx;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #404040;
          line-height: 44rpx;
        }
      }

      .title_right_text {
        font-size: 28rpx;
        color: #4378ff;
        background: #eaf2ff;
        border: 2rpx solid #4378ff;
        border-radius: 8rpx;
        padding: 4rpx 24rpx;
        cursor: pointer;
        transition:
          background 0.2s,
          color 0.2s;
        font-weight: 500;
        &:active {
          background: #4378ff;
          color: #fff;
        }
      }
    }
    .review-reason {
      margin-top: 16rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #ff3e3e;
      line-height: 34rpx;
    }
    .review-meta {
      display: flex;
      justify-content: space-between;
      color: #a09f9f;
      font-size: 24rpx;
      margin-top: 16rpx;
    }
    .meta-item {
      line-height: 36rpx;

      .meta-item-value {
        color: #404040;
      }
    }
  }
}
.review-card-empty {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(67, 120, 255, 0.08);
  padding: 24rpx;
  text-align: center;
  color: #a09f9f;
  font-size: 28rpx;
}
</style>

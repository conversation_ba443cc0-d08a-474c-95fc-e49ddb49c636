<template>
  <view class="select-container">
    <view class="select-header">
      <uv-search
        height="36"
        shape="square"
        searchIcon="/src/static/icon/search_icon.png"
        searchIconSize="16"
        :placeholder="searchPlaceholder"
        placeholderColor="#C1C1C1"
        bgColor="#fff"
        :showAction="false"
        :boxStyle="searchBoxStyle"
        v-model="searchValue"
        @change="onSearchChange"
        @search="onSearch"
      />
      <view v-if="selectedList.length">
        <view class="select-summary">
          <text class="select-summary-label">已添加</text>
          <text class="select-summary-count">{{ selectedList.length }}</text>
          <text class="select-summary-unit">{{ unitLabel }}</text>
        </view>
        <view class="select-tags">
          <template v-if="pageType === 'user'">
            <template v-if="selectedList.length > 6">
              <view
                class="select-tag"
                v-for="(item, idx) in selectedList.slice(0, 6)"
                :key="item.id"
              >
                {{ tagText(item) }}
              </view>
              <view class="select-tag select-tag-more">
                +{{ selectedList.length - 6 }}
              </view>
            </template>
            <template v-else>
              <view
                class="select-tag"
                v-for="(item, idx) in selectedList"
                :key="item.id"
              >
                {{ tagText(item) }}
              </view>
            </template>
          </template>
          <template v-else>
            <template v-if="selectedList.length > 1">
              <view class="select-tag-title">{{
                tagText(selectedList[0])
              }}</view>
              <view class="select-tag select-tag-more other"
                >+{{ selectedList.length - 1 }}</view
              >
            </template>
            <template v-else>
              <view
                class="select-tag-title"
                v-for="(item, idx) in selectedList"
                :key="item.id"
              >
                {{ tagText(item) }}
              </view>
            </template>
          </template>
        </view>
      </view>
    </view>
    <view class="select-content">
      <template v-if="!loading">
        <view v-if="filteredList.length">
          <view class="select-list">
            <view
              class="select-card"
              :class="{ 'select-card-disabled': item.disabled }"
              v-for="item in filteredList"
              :key="item.id"
              @click="toggleSelect(item)"
            >
              <view class="select-card-left">
                <image
                  v-if="isSelected(item)"
                  class="select-radio"
                  :src="radioActiveIcon"
                  mode="scaleToFill"
                />
                <image
                  v-else
                  class="select-radio"
                  :src="radioDefaultIcon"
                  mode="scaleToFill"
                />
              </view>
              <view class="select-card-right">
                <template v-if="pageType === 'user'">
                  <view class="select-card-avatar">{{
                    item.userName?.slice(-2)
                  }}</view>
                  <view class="select-card-info">
                    <view class="select-card-title">{{ item.userName }}</view>
                    <view class="select-card-desc">{{ item.deptName }}</view>
                  </view>
                </template>
                <template v-else-if="pageType === 'settlement'">
                  <view class="settlement-item-wrap">
                    <view class="select-card-title">{{
                      `【${item.code}】${item.content}`
                    }}</view>
                    <view class="select-card-extra">
                      {{ `¥${item.unitPrice}/${item.unitName || '-'}` }}
                    </view>
                  </view>
                </template>
                <template v-else-if="pageType === 'machine'">
                  <view class="machine-item-wrap">
                    <view class="select-card-title">{{ item.name }}</view>
                    <view class="select-card-desc"
                      >{{ item.administrator }}-{{ item.phone }}</view
                    >
                  </view>
                </template>
                <template v-else-if="pageType === 'material'">
                  <view class="material-item-wrap">
                    <view class="select-card-title">{{ item.typeName }}</view>
                    <view class="select-card-desc">{{
                      `${item.specification}/${item.unit || '-'}`
                    }}</view>
                  </view>
                  <view class="select-card-extra"
                    >剩余{{ item.inventory }}</view
                  >
                </template>
              </view>
            </view>
          </view>
        </view>
        <no-data class="select-no_data" v-else></no-data>
      </template>
      <uv-loading-page
        :loading="loading"
        loading-text="加载中..."
        font-size="24rpx"
      />
    </view>
    <view class="select-bottom">
      <view class="select-bottom-placeholder"></view>
      <view class="select-bottom-btn-box">
        <view
          class="select-bottom-btn"
          @click="onConfirm"
        >
          保 存
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref, computed, watch, onMounted, onUnmounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { RoutineMaintenanceService } from "@/service";
import NoData from "@/components/ylg-nodata.vue";
import eventHandling_radio_active from "@/static/icon/eventHandling_radio_active.png";
import eventHandling_radio_default from "@/static/icon/eventHandling_radio_default.png";

import { useProjectStore } from "@/store/project";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

// store
const projectInfo = useProjectStore();
const workOrderInfo = useRepairWorkOrderStore();

// 页面类型
const pageType = ref("");

// 搜索相关
const searchValue = ref("");
const searchBoxStyle = reactive({ borderRadius: "8px", margin: "48rpx 40rpx" });
const loading = ref(false);
const dataList = ref([]);
const filteredList = ref([]);
const selectedList = ref([]);

// 图标
const radioActiveIcon = eventHandling_radio_active;
const radioDefaultIcon = eventHandling_radio_default;

// 页面标题、单位、占位符映射
const titleMap = {
  user: "人员选择",
  settlement: "作业选择",
  machine: "设备选择",
  material: "材料选择",
};
const pageTitle = computed(() => titleMap[pageType.value] || "选择");
const searchPlaceholderMap = {
  user: "搜索人员姓名/部门",
  settlement: "搜索作业名称/描述",
  machine: "搜索设备名称",
  material: "搜索材料名称",
};
const unitLabelMap = {
  user: "人",
  settlement: "个作业内容",
  machine: "个设备",
  material: "个材料",
};
const searchPlaceholder = computed(
  () => searchPlaceholderMap[pageType.value] || "搜索"
);
const unitLabel = computed(() => unitLabelMap[pageType.value] || "");

// 监听标题变化
watch(
  pageType,
  () => {
    uni.setNavigationBarTitle({ title: pageTitle.value });
  },
  { immediate: true }
);

// 标签文本生成
const tagText = (item) => {
  if (pageType.value === "user") return item.userName?.slice(-2) || "";
  if (pageType.value === "settlement") return `【${item.code}】${item.content}` || "";
  if (pageType.value === "machine") return item.name || "";
  if (pageType.value === "material") return item.typeName || "";
  return "";
};

// 是否已选
const isSelected = (item) => selectedList.value.some((i) => i.id === item.id);

// 选中切换
const toggleSelect = (item) => {
  if (item.disabled) return; // 禁止已选项被再次选择
  if (isSelected(item)) {
    selectedList.value = selectedList.value.filter((i) => i.id !== item.id);
  } else {
    selectedList.value.push(item);
  }
};

// 搜索过滤
const filterList = () => {
  if (!searchValue.value) {
    filteredList.value = dataList.value;
    return;
  }
  filteredList.value = dataList.value.filter((item) => {
    if (pageType.value === "user") {
      return (
        item.userName?.includes(searchValue.value) ||
        item.deptName?.includes(searchValue.value)
      );
    }
    if (pageType.value === "settlement") {
      return (
        item.code?.includes(searchValue.value) ||
        item.name?.includes(searchValue.value)
      );
    }
    if (pageType.value === "machine") {
      return (
        item.name?.includes(searchValue.value) ||
        item.administrator?.includes(searchValue.value) ||
        item.phone?.includes(searchValue.value)
      );
    }
    if (pageType.value === "material") {
      return (
        item.typeName?.includes(searchValue.value) ||
        item.specification?.includes(searchValue.value) ||
        item.unit?.includes(searchValue.value) ||
        String(item.inventory).includes(searchValue.value)
      );
    }
    return false;
  });
};

// 搜索事件
const onSearchChange = (val) => {
  searchValue.value = val;
  filterList();
};
const onSearch = (val) => {
  searchValue.value = val;
  filterList();
};

// 数据获取
const fetchList = async (type) => {
  loading.value = true;
  let list = [];
  let params = { page: "1", limit: "999999" };
  try {
    if (type === "user") {
      params = { ...params, projectId: projectInfo.projectId };
      const { data } =
        await RoutineMaintenanceService.getMaintainUserList(params);
      list = data.map((item) => ({
        id: item.userId,
        userName: item.userName,
        deptName: item.deptName,
      }));
    } else if (type === "settlement") {
      params = { ...params, projectId: projectInfo.projectId };
      const { data } = await RoutineMaintenanceService.getMaintainWorkList(params);
      list = data.map((item) => ({
        id: item.id,
        content: item.name,
        code: item.code,
        unitPrice: item.unitPrice,
        unit: item.unit,
        unitName: item.unitName,
      }));
    } else if (type === "material") {
      // 先获取仓库列表并取第一个为warehouseId
      const { data: warehouseList } =
        await RoutineMaintenanceService.getWarehouseList({
          projectId: projectInfo.projectId,
        });
      params = {
        ...params,
        projectId: projectInfo.projectId,
        warehouseId: warehouseList[0] && warehouseList[0]?.id,
        inventoryNotZero: true,
      };
      // 获取材料列表
      const { data } =
        await RoutineMaintenanceService.getMaterialInformationList(params);
      list = data.map((item) => ({
        id: item.id,
        typeName: item.typeName,
        unit: item.unit,
        unitName: item.unitName,
        specification: item.specification,
        inventory: item.inventory,
      }));
    } else if (type === "machine") {
      params = { ...params, depId: workOrderInfo?.departmentId, status: "1" };
      const { data } =
        await RoutineMaintenanceService.getMachineInformationList(params);
      list = data.map((item) => ({
        id: item.id,
        name: item.name,
        phone: item.phone,
        administrator: item.administrator,
      }));
    }
  } catch (e) {
    list = [];
  }
  dataList.value = list;
  filterList();
  loading.value = false;
};

// 确认事件
const onConfirm = () => {
  uni.$emit("selectPageConfirm", {
    type: pageType.value,
    list: selectedList.value,
  });
  uni.navigateBack({ data: 1 });
};

// 页面加载
onLoad(async (options) => {
  if (options?.type) {
    pageType.value = options.type;
  }
  loading.value = true;
  await fetchList(pageType.value);
  let excludeIds = [];
  let selectedIds = [];
  if (options?.exclude) {
    try {
      excludeIds = JSON.parse(options.exclude);
    } catch (e) {
      excludeIds = [];
    }
  }
  if (options?.selected) {
    try {
      selectedIds = JSON.parse(options.selected);
    } catch (e) {
      selectedIds = [];
    }
  }
  // 先排除
  if (Array.isArray(excludeIds) && excludeIds.length) {
    dataList.value = dataList.value.filter(item => !excludeIds.includes(item.id));
    filteredList.value = dataList.value;
  }
  // 再选择已选项
  if (Array.isArray(selectedIds) && selectedIds.length) {
    selectedList.value = dataList.value.filter(item => selectedIds.includes(item.id));
  } else {
    selectedList.value = [];
  }
  loading.value = false;
});
</script>

<style lang="scss" scoped>
.select-container {
  height: 100vh;
  background: #f2f2f2;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.select-header {
  flex-shrink: 0;

  .select-summary {
    height: 80rpx;
    display: flex;
    align-items: center;
    background: #fff;
    padding: 0 40rpx;
    font-size: 28rpx;
    .select-summary-label {
      color: #373737;
    }
    .select-summary-count {
      color: #4378ff;
      margin: 0 8rpx;
    }
    .select-summary-unit {
      color: #373737;
    }
  }

  .select-tags {
    display: flex;
    gap: 0 24rpx;
    padding: 0 40rpx 24rpx 40rpx;
    margin-bottom: 24rpx;
    background: #fff;

    .select-tag-title {
      font-size: 28rpx;
      color: #373737;
      background-color: #f0f0f0;
      padding: 12rpx;
      margin-right: 8rpx;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .select-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #4378ff;
      font-size: 28rpx;
      width: 76rpx;
      height: 76rpx;
      line-height: 76rpx;
      border-radius: 8rpx;
      color: #fff;
      font-weight: 400;
    }

    .other {
      width: unset;
      height: unset;
      line-height: unset;
      font-size: 28rpx;
      color: #373737;
      padding: 12rpx;
      background: #f0f0f0;
    }
  }
}

.select-content {
  flex: 1;
  overflow-y: auto;

  .select-list {
    .select-card {
      display: flex;
      min-height: 140rpx;
      background: #fff;
      font-size: 32rpx;
      font-weight: 400;
      .select-card-left {
        width: 126rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .select-radio {
          width: 56rpx;
          height: 56rpx;
        }
      }
      .select-card-right {
        display: flex;
        align-items: center;
        gap: 0 38rpx;
        width: calc(100% - 126rpx);
        border-bottom: 2px solid #f2f2f2;
        padding: 24rpx 40rpx 24rpx 0;
        min-height: 140rpx;
        box-sizing: border-box;
        .settlement-item-wrap {
          display: flex;
          flex-direction: column;
          width: 100%;
        }

        .select-card-avatar {
          width: 76rpx;
          height: 76rpx;
          background: #4378ff;
          border-radius: 8rpx;
          color: #fff;
          font-size: 28rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }
        .select-card-info {
          display: flex;
          flex-direction: column;
          flex: 1;
          justify-content: center;
          min-height: 76rpx;
        }
        .select-card-title {
          font-size: 32rpx;
          color: #373737;
          line-height: 52rpx;
          margin-bottom: 8rpx;
          flex: 1;
          display: flex;
          align-items: center;
        }
        .select-card-desc {
          font-size: 28rpx;
          color: #a09f9f;
          line-height: 40rpx;
          flex: 1;
          display: flex;
          align-items: center;
        }
        .select-card-extra {
          font-size: 28rpx;
          color: #a09f9f;
          margin-left: auto;
          display: flex;
          align-items: center;
          flex-shrink: 0;
        }
      }
    }
  }
}

.select-no_data {
  margin-top: 136rpx;
}
.select-bottom {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  .select-bottom-placeholder {
    width: 100%;
    height: 26rpx;
    background: #f2f2f2;
    flex-shrink: 0;
  }
  .select-bottom-btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 160rpx;
    background: #fff;
    box-shadow: 0rpx -6rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    .select-bottom-btn {
      width: 670rpx;
      height: 84rpx;
      background: #4378ff;
      border-radius: 8rpx;
      font-size: 40rpx;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      transition: all 0.3s ease;
    }
  }
}
.select-card-disabled {
  opacity: 0.5;
  pointer-events: none;
}
</style>

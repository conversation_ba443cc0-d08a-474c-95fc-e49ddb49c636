<template>
    <view class="select-container">
      <view class="select-header">
        <uv-search
          height="36"
          shape="square"
          searchIcon="/src/static/icon/search_icon.png"
          searchIconSize="16"
          :placeholder="searchPlaceholder"
          placeholderColor="#C1C1C1"
          bgColor="#fff"
          :showAction="false"
          :boxStyle="searchBoxStyle"
          v-model="searchValue"
          @change="onSearchChange"
          @search="onSearch"
        />
        <view v-if="selectedList.length">
          <view class="select-summary">
            <text class="select-summary-label">已添加</text>
            <text class="select-summary-count">{{ selectedList.length }}</text>
            <text class="select-summary-unit">{{ unitLabel }}</text>
          </view>
          <view class="select-tags">
            <template v-if="config && config.tagMax && selectedList.length > config.tagMax">
              <view
                class="select-tag"
                v-for="(item, idx) in selectedList.slice(0, config.tagMax)"
                :key="item.id"
              >
                {{ tagText(item) }}
              </view>
              <view class="select-tag select-tag-more" v-if="config.tagShowPlus">
                +{{ selectedList.length - config.tagMax }}
              </view>
            </template>
            <template v-else-if="config && config.tagMax === 1 && selectedList.length > 1">
              <view class="select-tag-title">{{ tagText(selectedList[0]) }}</view>
              <view class="select-tag select-tag-more other" v-if="config.tagShowPlus">
                +{{ selectedList.length - 1 }}
              </view>
            </template>
            <template v-else>
              <view
                class="select-tag-title"
                v-for="(item, idx) in selectedList"
                :key="item.id"
              >
                {{ tagText(item) }}
              </view>
            </template>
          </view>
        </view>
      </view>
      <view class="select-content">
        <template v-if="!loading">
          <view v-if="filteredList.length">
            <view class="select-list">
              <view
                class="select-card"
                v-for="item in filteredList"
                :key="item.id"
                @click="toggleSelect(item)"
              >
                <view class="select-card-left">
                  <image
                    v-if="isSelected(item)"
                    class="select-radio"
                    :src="radioActiveIcon"
                    mode="scaleToFill"
                  />
                  <image
                    v-else
                    class="select-radio"
                    :src="radioDefaultIcon"
                    mode="scaleToFill"
                  />
                </view>
                <view class="select-card-right">
                  <component :is="config.render(item)" />
                </view>
              </view>
            </view>
          </view>
          <no-data class="select-no_data" v-else></no-data>
        </template>
        <uv-loading-page
          :loading="loading"
          loading-text="加载中..."
          font-size="24rpx"
        />
      </view>
      <view class="select-bottom">
        <view class="select-bottom-placeholder"></view>
        <view class="select-bottom-btn-box">
          <view class="select-bottom-btn" @click="onConfirm">保 存</view>
        </view>
      </view>
    </view>
  </template>
  
  <script setup>
  import { reactive, ref, computed, watch } from "vue";
  import { onLoad } from "@dcloudio/uni-app";
  import NoData from "@/components/ylg-nodata.vue";
  import eventHandling_radio_active from "@/static/icon/eventHandling_radio_active.png";
  import eventHandling_radio_default from "@/static/icon/eventHandling_radio_default.png";
  import { useSelectPageConfig } from "./hooks/useSelectPageConfig";
  import { useProjectStore } from "@/store/project";
  import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";
  
  // ===================== 数据 =====================
  const projectStore = useProjectStore();
  const workOrderStore = useRepairWorkOrderStore();
  const pageType = ref("user");
  const backType = ref("");
  const searchValue = ref("");
  const searchBoxStyle = reactive({ borderRadius: "8px", margin: "48rpx 40rpx" });
  const loading = ref(false);
  const dataList = ref([]);
  const filteredList = ref([]);
  const selectedList = ref([]);
  const radioActiveIcon = eventHandling_radio_active;
  const radioDefaultIcon = eventHandling_radio_default;
  
  // ===================== 计算属性 =====================
  const { config } = useSelectPageConfig(projectStore, workOrderStore, pageType);
  const pageTitle = computed(() => config.value.title || "选择");
  const searchPlaceholder = computed(() => config.value.searchPlaceholder || "搜索");
  const unitLabel = computed(() => config.value.unitLabel || "");
  const tagText = item => config.value.tagText ? config.value.tagText(item) : "";
  
  // ===================== watch =====================
  watch(pageType, () => {
    uni.setNavigationBarTitle({ title: pageTitle.value });
  }, { immediate: true });
  
  // ===================== 方法 =====================
  const isSelected = (item) => selectedList.value.some((i) => i.id === item.id);
  
  const toggleSelect = (item) => {
    if (isSelected(item)) {
      selectedList.value = selectedList.value.filter((i) => i.id !== item.id);
    } else {
      selectedList.value.push(item);
    }
  };
  
  const onSearchChange = (val) => {
    searchValue.value = val;
    filterList();
  };
  const onSearch = (val) => {
    searchValue.value = val;
    filterList();
  };
  
  function filterList() {
    if (!searchValue.value) {
      filteredList.value = dataList.value;
      return;
    }
    const fields = config.value.filterFields || [];
    filteredList.value = dataList.value.filter(item =>
      fields.some(field => String(item[field] ?? '').includes(searchValue.value))
    );
  }
  
  async function fetchList() {
    loading.value = true;
    try {
      if (config.value.fetch) {
        dataList.value = await config.value.fetch();
      } else {
        dataList.value = [];
      }
    } catch (e) {
      dataList.value = [];
    }
    filterList();
    loading.value = false;
  }
  
  const onConfirm = () => {
    uni.$emit("selectPageConfirm", { type: backType.value, list: selectedList.value });
    uni.navigateBack({ data: 1 });
  };
  
  // ===================== 生命周期 =====================
  onLoad(async (options) => {
    if (options?.type) {
      pageType.value = options.type;
      backType.value = options.type;    
    }
    loading.value = true;
    await fetchList();
    if (options?.selected) {
      try {
        selectedList.value = JSON.parse(options.selected);
      } catch (e) {
        selectedList.value = [];
      }
    }
    loading.value = false;
  });
  </script>
  
  <style lang="scss" scoped>
  .select-container {
    height: calc(100vh - 44px - env(safe-area-inset-top));
    background: #f2f2f2;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  .select-header {
    flex-shrink: 0;
  
    .select-summary {
      height: 80rpx;
      display: flex;
      align-items: center;
      background: #fff;
      padding: 0 40rpx;
      font-size: 28rpx;
      .select-summary-label {
        color: #373737;
      }
      .select-summary-count {
        color: #4378ff;
        margin: 0 8rpx;
      }
      .select-summary-unit {
        color: #373737;
      }
    }
  
    .select-tags {
      display: flex;
      gap: 0 24rpx;
      padding: 0 40rpx 24rpx 40rpx;
      margin-bottom: 24rpx;
      background: #fff;
  
      .select-tag-title {
        font-size: 28rpx;
        color: #373737;
        background-color: #f0f0f0;
        padding: 12rpx;
        margin-right: 8rpx;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
  
      .select-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #4378ff;
        font-size: 28rpx;
        width: 76rpx;
        height: 76rpx;
        line-height: 76rpx;
        border-radius: 8rpx;
        color: #fff;
        font-weight: 400;
      }
  
      .other {
        width: unset;
        height: unset;
        line-height: unset;
        font-size: 28rpx;
        color: #373737;
        padding: 12rpx;
        background: #f0f0f0;
      }
    }
  }
  
  .select-content {
    flex: 1;
    overflow-y: auto;
  
    .select-list {
      .select-card {
        display: flex;
        min-height: 140rpx;
        background: #fff;
        font-size: 32rpx;
        font-weight: 400;
        .select-card-left {
          width: 126rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          .select-radio {
            width: 56rpx;
            height: 56rpx;
          }
        }
        .select-card-right {
          display: flex;
          align-items: center;
          gap: 0 38rpx;
          width: calc(100% - 126rpx);
          border-bottom: 2px solid #f2f2f2;
          padding: 24rpx 40rpx 24rpx 0;
          min-height: 140rpx;
          box-sizing: border-box;
          .work-item-wrap {
            display: flex;
            flex-direction: column;
            width: 100%;
          }
  
          .select-card-avatar {
            width: 76rpx;
            height: 76rpx;
            background: #4378ff;
            border-radius: 8rpx;
            color: #fff;
            font-size: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
          }
          .select-card-info {
            display: flex;
            flex-direction: column;
            flex: 1;
            justify-content: center;
            min-height: 76rpx;
          }
          .select-card-title {
            font-size: 32rpx;
            color: #373737;
            line-height: 52rpx;
            margin-bottom: 8rpx;
            flex: 1;
            display: flex;
            align-items: center;
          }
          .select-card-desc {
            font-size: 28rpx;
            color: #a09f9f;
            line-height: 40rpx;
            flex: 1;
            display: flex;
            align-items: center;
          }
          .select-card-extra {
            font-size: 28rpx;
            color: #a09f9f;
            margin-left: auto;
            display: flex;
            align-items: center;
            flex-shrink: 0;
          }
        }
      }
    }
  }
  
  .select-no_data {
    margin-top: 136rpx;
  }
  .select-bottom {
    width: 100%;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    .select-bottom-placeholder {
      width: 100%;
      height: 26rpx;
      background: #f2f2f2;
      flex-shrink: 0;
    }
    .select-bottom-btn-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 160rpx;
      background: #fff;
      box-shadow: 0rpx -6rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
      flex-shrink: 0;
      .select-bottom-btn {
        width: 670rpx;
        height: 84rpx;
        background: #4378ff;
        border-radius: 8rpx;
        font-size: 40rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
      }
    }
  }
  </style>
  
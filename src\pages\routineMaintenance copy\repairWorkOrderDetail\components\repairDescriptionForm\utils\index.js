// 工具函数

/**
 * 获取单位标签
 * @param {string} units - 单位值
 * @param {Array} list - 单位列表
 * @returns {string} 单位标签
 */
export function getUnitLabel(units, list) {
  const found = list.find((u) => u.value === units);
  return found ? found.label : list[0].label;
}

/**
 * 获取数量值
 * @param {Object} item - 数据项
 * @param {boolean} isAcceptance - 是否为验收模式
 * @param {string} planKey - 计划数量字段名
 * @param {string} realKey - 实际数量字段名
 * @returns {number|string} 数量值
 */
export function getNum(item, isAcceptance, planKey = "planNum", realKey = "realNum") {
  return isAcceptance ? item[realKey] : item[planKey];
}

/**
 * 格式化价格显示
 * @param {number|string} price - 价格
 * @param {string} unit - 单位
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(price, unit) {
  return `¥${price || "-"}/${unit || "-"}`;
}

/**
 * 检查是否需要显示改进数据图标
 * @param {Array} list - 数据列表
 * @param {Array} checkFields - 需要检查的字段
 * @returns {boolean} 是否显示图标
 */
export function shouldShowImproveIcon(list, checkFields = ['unitPrice', 'realNum']) {
  if (!list || !Array.isArray(list)) return false;
  return list.some(item => 
    checkFields.some(field => !item[field])
  );
}

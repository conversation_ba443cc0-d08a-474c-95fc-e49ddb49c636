<template>
  <view class="completed-container">
    <view class="completed-content">
      <view style="padding: 0 40rpx">
        <view>
          <ConstructionCard
            :baseInfo="repairWorkOrderBaseInfo"
            :detailInfo="repairWorkOrderDetail"
            :childOrderList="repairChildWorkOrderList"
            :configDetail="workOrderConfigDetail"
            :isShowBar="true"
          />
          <view class="review-record-wrap">
            <ReviewRecordCard
              :baseInfo="repairWorkOrderBaseInfo"
              :checkLogList="repairWorkOrderDetail.vericaContent || []"
              @goAcceptanceMeasurement="goAcceptanceMeasurement"
            />
            <TaskCompletionCard
              :completeInfo="childWorkOrderCompleteInfo"
              @goDetail="goChildWorkOrderDetail"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import ConstructionCard from "../components/card/ConstructionCard.vue";
import ReviewRecordCard from "../components/card/ReviewRecordCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => {},
  },
  // 工单详情
  repairWorkOrderDetail: {
    type: Object,
    default: () => {},
  },
  // 子工单完成情况
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => {},
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Object,
    default: () => [],
  },
  // 工单资源信息
  workOrderConfigDetail: {
    type: Object,
    default: () => {},
  },
});

const goAcceptanceMeasurement = (index) => {
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/acceptanceDetail?isViewMode=${true}&index=${index}`,
  });
};

const goChildWorkOrderDetail = (info) => {
  const id = props.repairWorkOrderBaseInfo?.id;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index?workId=${id}&childWorkId=${info.workId}`,
  });
};
</script>

<style lang="scss" scoped>
.completed-container {
  background-color: #f4f8ff;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.completed-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;

  .review-record-wrap {
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
  }
}
</style>

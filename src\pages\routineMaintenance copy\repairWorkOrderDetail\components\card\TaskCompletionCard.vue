<template>
  <view class="task-card">
    <view class="card-title-row">
      <view class="blue-bar"></view>
      <text class="bar-text">任务完成情况</text>
    </view>
    <view class="field-row"
      ><text class="field-label">开始时间：</text
      ><text class="field-value">{{ startTime }}</text></view
    >
    <view class="field-row"
      ><text class="field-label">开始定位：</text
      ><text class="field-value">{{ startLocation }}</text></view
    >
    <view class="field-row"
      ><text class="field-label">任务用时：</text
      ><text class="field-value">{{ duration }}</text></view
    >
    <view class="tab-row" v-if="showTabList">
      <view
        v-for="tab in tabList"
        :key="tab.key"
        :class="['tab', { active: tab.active }]"
        @click.stop="handleTabChange(tab.key)"
      >
        {{ tab.label }}
      </view>
    </view>
    <view class="order-list">
      <view v-if="orderList.length === 0" class="order-item">
        <text class="order-title">暂无数据</text>
      </view>
      <view
        v-for="item in orderList"
        :key="item.id"
        class="order-item"
        @click.stop="goDetail(item)"
      >
        <view class="order-main">
          <text class="order-title">{{ item.diseasesTypeName }}</text>
          <text class="order-sub">{{ item.eventObjectName }}</text>
        </view>
        <view class="order-meta">
          <text class="order-time">{{ item.createTime }}</text>
          <text
            v-if="currentTab === 'reject' && hasPermission"
            class="order-action"
            @click.stop="emit('toReReport', { ...item, flag: orderList?.length })"
            >重新上报</text
          >
          <text v-else></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { useUserStore } from "@/store/user";

// 用户store
const userInfo = useUserStore();

const props = defineProps({
  completeInfo: {
    type: Object,
    default: () => ({}),
  },
  showTabList: {
    type: Boolean,
    default: false,
  },
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 维修配置信息
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["goDetail", "toReReport"]);

// 权限检查计算属性 - 只有在显示 tab 列表时才需要检查权限
const hasPermission = computed(() => {
  // 如果不显示 tab 列表，则不需要权限检查（不会显示重新上报按钮）
  if (!props.showTabList) {
    return false;
  }

  // 检查资源权限
  const hasResourcePermission =
    props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairWork") ||
    false;

  // 检查当前用户是否在工单用户列表中
  const currentUserId = userInfo.id;
  const isUserInWorkOrder =
    props.workOrderConfigDetail?.users?.some(
      (user) => user.relevancyId === currentUserId
    ) || false;

  return hasResourcePermission && isUserInWorkOrder;
});

// tab 列表
const tabList = ref([
  { key: "reject", label: "被驳回", active: false },
  { key: "ver", label: "待核验", active: true },
]);

// 当前 tab
const currentTab = ref("ver");
watch(
  () => props.showTabList,
  (val) => {
    if (val) {

      handleTabChange("reject");
    }
  },
  {
    immediate: true,
  }
);

// 切换 tab
function handleTabChange(key) {
  tabList.value.forEach((tab) => (tab.active = tab.key === key));
  currentTab.value = key;
}

function goDetail(item) {
  emit("goDetail", item);
}

// 任务基础信息
const startTime = computed(() => props.completeInfo.startTime || "");
const startLocation = computed(() => props.completeInfo.address || "");
const duration = computed(() => props.completeInfo.time || "");

// 订单列表（根据 tab 展示 ver 或 reject）
const orderList = computed(() => props.completeInfo[currentTab.value] || []);
</script>

<style lang="scss" scoped>
@import "../../common.scss";

.task-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(67, 120, 255, 0.08);
  padding: 24rpx;
}
.card-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.field-row {
  display: flex;
  margin-bottom: 8rpx;
}
.field-label {
  flex-shrink: 0;
  color: #a09f9f;
  font-size: 28rpx;
  margin-right: 8rpx;
}
.field-value {
  color: #373737;
  font-size: 28rpx;
}
.tab-row {
  display: flex;
  margin: 24rpx 0;
}
.tab {
  flex: 1;
  margin-top: 24rpx;
  height: 56rpx;
  line-height: 56rpx;
  background: #f5f7fa;
  color: #a09f9f;
  font-size: 28rpx;
  text-align: center;
  // 不设置通用圆角
}
.tab:first-child {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.tab:last-child {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}
.tab.active {
  background: #4378ff;
  color: #fff;
}
.order-list {
  margin-top: 32rpx;
}
.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #e9e9e9;
  padding: 20rpx 24rpx;
  margin-bottom: 16rpx;
}
.order-main {
  display: flex;
  flex-direction: column;
}
.order-title {
  color: #373737;
  font-size: 28rpx;
  font-weight: 500;
}
.order-sub {
  color: #a09f9f;
  font-size: 24rpx;
  margin-top: 4rpx;
}
.order-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}
.order-time {
  color: #a09f9f;
  font-size: 24rpx;
}
.order-action {
  color: #4378ff;
  font-size: 24rpx;
  margin-top: 4rpx;
}
</style>

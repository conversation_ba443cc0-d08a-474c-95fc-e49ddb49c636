<template>
  <view class="work-order-verification-container">
    <view class="work-order-verification-content">
      <view class="content-padding">
        <!-- 施工信息卡片 -->
        <ConstructionCard
          :base-info="workOrderBaseInfo"
          :detail-info="childWorkOrderDetail"
          :child-order-list="childWorkOrderList"
          :config-detail="workOrderConfigDetail"
          :is-show-bar="true"
        />
        <!-- 任务完成情况卡片 -->
        <view class="task-completion-section">
          <TaskCompletionCard
            :complete-info="childWorkOrderCompleteInfo"
            @go-detail="handleGoChildWorkOrderDetail"
          />
        </view>
      </view>
    </view>

    <!-- 底部操作按钮区域 -->
    <view class="footer-action-area">
      <!-- 驳回按钮 -->
      <button
        class="action-button action-button--reject"
        @click="handleRejectOrder"
      >
        驳回
      </button>
      <!-- 验收通过按钮 -->
      <button
        class="action-button action-button--pass"
        @click="handlePassOrder"
      >
        验收通过
      </button>
    </view>
  </view>
</template>

<script setup>
// ==================== 导入依赖 ====================
import { computed } from "vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";
import { buildUrlWithParams } from "@/utils";

// ==================== Props定义 ====================
const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  // 子工单详情
  repairChildWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 工单配置详情
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单完成情况信息
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => ({}),
  },
});

// ==================== 计算属性 ====================
// 规范化的数据引用
const workOrderBaseInfo = computed(() => props.repairWorkOrderBaseInfo || {});
const childWorkOrderList = computed(() => props.repairChildWorkOrderList || []);
const childWorkOrderDetail = computed(
  () => props.repairChildWorkOrderDetail || {}
);

// ==================== 事件定义 ====================
const emit = defineEmits(["getRepairChildWorkOrderDetail"]);

// ==================== 事件处理方法 ====================
/**
 * 处理子工单详情跳转
 * @param {Object} info - 子工单信息
 */
const handleGoChildWorkOrderDetail = (info) => {
  const params = {
    workId: workOrderBaseInfo.value?.id, // 工单ID
    childWorkId: info.workId, // 子工单ID
  };
  uni.navigateTo({
    url: buildUrlWithParams(
      "/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index",
      params
    ),
  });
};

/**
 * 处理驳回操作
 * 跳转到驳回页面
 */
const handleRejectOrder = () => {
  uni.navigateTo({
    url: `/pages/common/rejectedPage?type=repairWorkOrder`,
  });
};

/**
 * 处理验收通过操作
 * 跳转到验收通过页面
 */
const handlePassOrder = () => {
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/acceptanceDetail`,
  });
};
</script>

<style lang="scss" scoped>
@import "../common.scss";

.work-order-verification-container {
  background-color: #f4f8ff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .work-order-verification-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;

    .content-padding {
      padding: 0 40rpx;
    }

    .task-completion-section {
      padding-top: 40rpx;
    }
  }

  .footer-action-area {
    height: 160rpx;
    flex-shrink: 0;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background: transparent;
    gap: 40rpx;
    padding: 0 40rpx;
    box-sizing: border-box;

    .action-button {
      height: 96rpx;
      line-height: 96rpx;
      border-radius: 8rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600;
      font-size: 40rpx;
      border: none;
      outline: none;
      transition: all 0.2s ease;

      &--reject {
        flex: 1;
        background: #dddddd;
        color: #62697b;

        &:active {
          background: #cccccc;
          transform: scale(0.99);
        }
      }

      &--pass {
        flex: 2;
        background: #4378ff;
        color: #ffffff;
        box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);

        &:active {
          background: #3366ee;
          transform: scale(0.99);
        }
      }
    }
  }
}
</style>

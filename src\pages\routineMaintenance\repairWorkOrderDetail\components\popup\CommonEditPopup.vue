<template>
  <view v-if="visible" class="common-modal-mask">
    <view class="common-modal">
      <view class="common-modal-header">
        <view class="common-modal-title">{{ title }}</view>
        <view class="common-modal-close" @click="$emit('close')">×</view>
      </view>
      <view class="common-modal-body">
        <!-- 信息区 -->
        <view v-if="info" class="common-modal-info">
          <view
            v-if="info.avatar || info.avatarText"
            class="common-modal-avatar"
          >
            <image
              v-if="info.avatar"
              :src="info.avatar"
              class="common-modal-avatar-img"
            />
            <template v-else>{{ info.avatarText }}</template>
          </view>
          <view class="common-modal-info-main">
            <view class="common-modal-info-title">{{ info.title }}</view>
            <view v-if="info.sub" class="common-modal-info-sub">{{
              info.sub
            }}</view>
            <view v-if="info.extra" class="common-modal-info-extra">{{
              info.extra
            }}</view>
          </view>
        </view>
        <view class="common-modal-info-divider"></view>
        <!-- 表单字段列表，支持通过 field.isNotShow 控制整行显示/隐藏 -->
        <template v-for="field in fields" :key="field.key">
          <view
            v-if="!field.isNotShow"
            :class="[
              'common-modal-row',
              field.type === 'planTotal' ||
              field.type === 'realTotal' ||
              field.type === 'countCost'
                ? 'cost-row'
                : '',
              field.type === 'realTotal' ? 'no-margin-top' : '',
            ]"
          >
            <view class="common-modal-label">
              <template v-if="field.bar"
                ><text class="blue-bar"></text
              ></template>
              {{ field.label }}
              <template v-if="field.required">
                <image
                  class="required-icon"
                  src="@/static/icon/required_icon.png"
                />
              </template>
            </view>
            <template v-if="field.type === 'unit'">
              <view class="common-modal-unit-list">
                <view
                  v-for="unit in unitList"
                  :key="unit.value"
                  class="common-modal-unit"
                  :class="{ active: form[field.key] === unit.value }"
                  @click="!isViewMode && (form[field.key] = unit.value)"
                >
                  {{ unit.label || "-" }}
                </view>
              </view>
            </template>
            <template v-else-if="field.type === 'input'">
              <input
                class="common-modal-input"
                :class="{ 'input-disabled': field.disable || isViewMode }"
                v-model="form[field.key]"
                :type="field.inputType || 'text'"
                :placeholder="
                  field.placeholder ||
                  (field.disable || isViewMode ? '-' : '请输入')
                "
                @blur="onInputBlur(field.key)"
                :disabled="field.disable || isViewMode"
              />
            </template>
            <template
              v-else-if="
                field.type === 'planTotal' || field.type === 'realTotal'
              "
            >
              <text class="common-modal-cost">
                ￥{{
                  form[field.key] !== undefined &&
                  form[field.key] !== null &&
                  form[field.key] !== ""
                    ? Number(form[field.key]).toFixed(2)
                    : "0.00"
                }}<span class="common-modal-cost-unit">元</span>
              </text>
            </template>
            <template v-else-if="field.type === 'countCost'">
              <text class="common-modal-cost">
                ￥{{ countCost || "0.00"
                }}<span class="common-modal-cost-unit">元</span>
              </text>
            </template>
          </view>
        </template>
        <view class="common-modal-info-divider"></view>
      </view>
      <view class="common-modal-footer" v-if="!isViewMode">
        <button
          v-if="showDelete"
          class="common-modal-btn delete"
          @click="$emit('delete')"
        >
          删除
        </button>
        <button
          class="common-modal-btn confirm"
          :class="{ disabled: !isFormValid }"
          :disabled="!isFormValid"
          @click="onConfirm"
        >
          确定
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, reactive, watch } from "vue";
import Decimal from "decimal.js";
const props = defineProps({
  visible: Boolean,
  title: String,
  fields: Array,
  model: Object,
  unitList: Array,
  showDelete: Boolean,
  info: Object,
  isAcceptance: Boolean,
  isViewMode: Boolean,
});
const emit = defineEmits(["close", "confirm", "delete"]);
const form = reactive({});
watch(
  () => props.model,
  (val) => {
    // 先清空form
    Object.keys(form).forEach((k) => delete form[k]);
    if (val) {
      Object.assign(form, val);
    }
  },
  { immediate: true }
);
// 计算花费
const countCost = computed(() => {
  // 默认：count * price，使用Decimal保证精度，保留两位小数，四舍五入
  const c = new Decimal(
    (props.isAcceptance
      ? form.realNum || form.realNumber
      : form.planNum || form.planNumber) || 0
  );
  const p = new Decimal(form.unitPrice || 0);
  let result = c.times(p);
  const MAX = new Decimal("9999998000.00");
  if (result.greaterThan(MAX)) {
    result = MAX;
  }
  return result.toFixed(2); // 字符串，两位小数，四舍五入
});

/**
 * 检查表单是否有效（所有必填字段都已填写）
 * 用于控制确定按钮的启用/禁用状态
 */
const isFormValid = computed(() => {
  // 过滤出显示的必填字段（排除被 isNotShow 隐藏的字段）
  const visibleRequiredFields = props.fields.filter(
    (field) => field.required && !field.isNotShow
  );

  // 检查所有必填字段是否都有值
  return visibleRequiredFields.every((field) => {
    const value = form[field.key];
    // 检查值是否存在且不为空字符串（包括空格）
    return value !== undefined && value !== null && String(value).trim() !== "";
  });
});

function onConfirm() {
  // 如果表单无效，直接返回
  if (!isFormValid.value) {
    return;
  }

  // 校验必填（双重保险）
  for (const field of props.fields) {
    if (field.required && !field.isNotShow && !form[field.key]) {
      uni.showToast({
        title: `请填写${field.label}`,
        icon: "none",
      });
      return;
    }
  }
  emit("confirm", { ...props.model, ...form, countCost: countCost.value });
}
// 输入框失去焦点时，自动保留2位小数并四舍五入
function onInputBlur(key) {
  if (
    (key === "unitPrice" || key === "planNum") &&
    form[key] !== undefined &&
    form[key] !== null &&
    form[key] !== ""
  ) {
    try {
      let d = new Decimal(form[key]);
      // 限制最大值
      if (d.greaterThan(99999.99)) {
        d = new Decimal(99999.99);
      }
      form[key] = d.toFixed(2);
    } catch (e) {
      // 非法输入不处理
    }
  }
}
</script>

<style lang="scss" scoped>
.common-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.common-modal {
  width: 100vw;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  animation: popup 0.2s;
}
@keyframes popup {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
.common-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 0 40rpx;
}
.common-modal-title {
  font-size: 41rpx;
  font-weight: 600;
  color: #222;
}
.common-modal-close {
  font-size: 48rpx;
  color: #a0a0a0;
  cursor: pointer;
}
.common-modal-body {
  padding: 0 40rpx;
}
.common-modal-info {
  display: flex;
  align-items: center;
  margin-top: 32rpx;
  margin-bottom: 8rpx;
  .common-modal-avatar {
    width: 76rpx;
    height: 76rpx;
    background: #4378ff;
    color: #fff;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 500;
    margin: 0 16rpx 0 0;
    flex-shrink: 0;
    .common-modal-avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
      object-fit: cover;
    }
  }
  .common-modal-info-main {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8rpx;
    .common-modal-info-title {
      font-size: 36rpx;
      color: #373737;
      font-weight: 500;
      line-height: 1.2;
      word-break: break-all;
    }
    .common-modal-info-sub {
      font-size: 32rpx;
      color: #a0a0a0;
      line-height: 1.2;
      word-break: break-all;
    }
    .common-modal-info-extra {
      font-size: 30rpx;
      color: #b0b0b0;
      line-height: 1.2;
      margin-top: 2rpx;
    }
  }
}
.common-modal-info-divider {
  height: 2rpx;
  background: #f2f2f2;
  margin: 24rpx 0 0 0;
  width: 100%;
}
.common-modal-row {
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  flex-wrap: nowrap;
  .common-modal-label {
    font-size: 32rpx;
    color: #222;
    font-weight: 500;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    .blue-bar {
      width: 6rpx;
      height: 28rpx;
      background: #4378ff;
      border-radius: 4rpx;
      margin-right: 12rpx;
      display: inline-block;
    }
    .required-icon {
      width: 84rpx;
      height: 40rpx;
      margin-left: 8rpx;
      margin-top: 4rpx;
    }
  }
  .common-modal-unit-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 22rpx;
    margin-top: 0;
  }
  .common-modal-unit {
    width: 140rpx;
    height: 64rpx;
    background: #f7f8fa;
    border-radius: 12rpx;
    color: #222;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12rpx;
    padding: 0 32rpx;
    cursor: pointer;
    border: 2rpx solid transparent;
    &.active {
      background: #065bff;
      color: #fff;
      border: 2rpx solid #065bff;
    }
  }
  .common-modal-input {
    width: 100%;
    height: 64rpx;
    border-radius: 12rpx;
    background: #f7f8fa;
    border: none;
    font-size: 32rpx;
    padding: 0 24rpx;
    margin-left: 0;
    box-sizing: border-box;
  }
  .common-modal-readonly {
    font-size: 32rpx;
    color: #222;
    margin-left: 0;
    width: 100%;
  }
  .common-modal-cost {
    color: #065bff;
    font-size: 36rpx;
    font-weight: 600;
    margin-left: 0;
    margin-top: 8rpx;
    .common-modal-cost-unit {
      font-size: 27rpx;
      color: #222;
      font-weight: 400;
      margin-left: 4rpx;
    }
  }
}
.cost-row {
  flex-direction: row;
  align-items: center;

  .common-modal-label {
    margin-bottom: 0;
    margin-right: 24rpx;
    flex-shrink: 0;
    white-space: nowrap;
  }

  .common-modal-cost {
    margin-left: auto;
    margin-top: 0;
    font-size: 36rpx;
    font-weight: 600;
    color: #065bff;
  }
}

.no-margin-top {
  margin-top: 0 !important;
}

.common-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  gap: 32rpx;
}
.common-modal-btn {
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 36rpx;
  font-weight: 500;
  margin: 0 8rpx;
  border: none;
}
.common-modal-btn.delete {
  flex: 1;
  background: #f2f2f2;
  color: #a0a0a0;
}
.common-modal-btn.confirm {
  flex: 2;
  background: #065bff;
  color: #fff;

  &.disabled {
    background: #cccccc !important;
    color: #888888 !important;
    cursor: not-allowed;
    opacity: 0.6;
  }
}
.input-disabled {
  color: #888 !important;
  background: #eaeaea !important;
}
</style>

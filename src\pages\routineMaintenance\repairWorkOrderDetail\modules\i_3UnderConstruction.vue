<template>
  <view class="under-construction-container">
    <view class="under-construction-content">
      <view style="padding: 0 40rpx">
        <ConstructionCard
          :baseInfo="repairWorkOrderBaseInfo"
          :detailInfo="repairWorkOrderDetail"
          :childOrderList="repairChildWorkOrderList"
          :configDetail="workOrderConfigDetail"
          :pageLoading="pageLoading"
          :isShowBar="true"
        />
      </view>
      <!-- 正常流程 -->
      <view v-if="!childWorkOrderCompleteInfo.reject?.length">
        <view class="child-work-order-detail-wrap">
          <ChildWorkOrderDetail
            :childWorkOrderList="repairChildWorkOrderList"
            :childOrderDetail="repairChildWorkOrderDetail"
            :currentPage="currentChildOrderPage"
            :showReportBtn="true"
            :repairWorkOrderBaseInfo="repairWorkOrderBaseInfo"
            :workOrderConfigDetail="workOrderConfigDetail"
            mode="carousel"
            @pageChange="handleChildOrderPageChange"
            @report="handleReport"
          />
          <view
            v-if="repairChildWorkOrderList.length > 1"
            class="confirm-switcher-wrap"
          >
            <NumberSwitcher
              :currentPage="currentChildOrderPage"
              :totalPages="repairChildWorkOrderList.length"
              @change="handleChildOrderPageChange"
            />
          </view>
        </view>
      </view>
      <!-- 被驳回 -->
      <view v-else class="reject-info-wrap">
        <ReviewRecordCard
          :checkLogList="repairWorkOrderDetail.vericaContent || []"
        />
        <TaskCompletionCard
          :completeInfo="childWorkOrderCompleteInfo"
          :showTabList="true"
          :repairWorkOrderBaseInfo="repairWorkOrderBaseInfo"
          :workOrderConfigDetail="workOrderConfigDetail"
          @goDetail="goChildWorkOrderDetail"
          @toReReport="toReReport"
        />
      </view>
    </view>
    <view class="under-construction-bottom">
      <view class="progress-section">
        <view class="progress-text-row">
          <text class="progress-label">维修任务进度</text>
          <text class="progress-value"
            >{{ finishedCount }}/{{ repairChildWorkOrderList.length }}</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import ReviewRecordCard from "../components/card/ReviewRecordCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => {},
  },
  // 工单详情(审核记录)
  repairWorkOrderDetail: {
    type: Object,
    default: () => {},
  },
  // 作业详情
  repairChildWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单完成情况
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => {},
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Object,
    default: () => [],
  },
  // 工单资源信息
  workOrderConfigDetail: {
    type: Object,
    default: () => {},
  },
  // 数据加载
  pageLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "getRepairChildWorkOrderDetail"
]);

let currentChildOrderPage = ref(1);

// 已完成的子工单数量
const finishedCount = computed(
  () =>
    props.repairChildWorkOrderList?.filter((item) => item.workStatus === "4")
      .length
);

// 子工单页切换
function handleChildOrderPageChange(page) {
  currentChildOrderPage.value = page;
  // 请求工单详情
  emit("getRepairChildWorkOrderDetail", childOrderList[page - 1]?.eventId);
}

function handleReport(item, index) {
  // 作业上报
  console.log("作业上报", item, index);
  const finishedNum = props.repairChildWorkOrderList?.filter(
    (item) => item.workStatus == "4"
  ).length;
  const flag = !!(
    props.repairChildWorkOrderList.length - finishedNum.value ==
    1
  );
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/report?pageFrom=startBuild&workId=${item.parentId}&childWorkId=${item.id}&saveStatus=${item.saveStatus}&childWorkCode=${item.workCode}&isLastChild=${flag}`,
  });
}

const goChildWorkOrderDetail = (info) => {
  const id = props.repairWorkOrderBaseInfo?.id;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index?workId=${id}&childWorkId=${info.workId}`,
  });
};

const toReReport = (info) => {
  const id = props.repairWorkOrderBaseInfo?.id;
  const { childWorkId, saveStatus, childWorkCode, flag } = info;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/report?workId=${id}&childWorkId=${childWorkId}&saveStatus=${saveStatus}&childWorkCode=${childWorkCode}&isLastChild=${flag}`,
  });
};
</script>

<style lang="scss" scoped>
.under-construction-container {
  background-color: #f4f8ff;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.under-construction-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;

  .child-work-order-detail-wrap {
    margin-top: 40rpx;
  }

  .reject-info-wrap {
    margin-top: 40rpx;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
  }

  .confirm-switcher-wrap {
    margin: 32rpx 0;
  }
}
.under-construction-bottom {
  flex-shrink: 0;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;

  .progress-section {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    padding: 32rpx 40rpx 32rpx 40rpx;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(68, 76, 108, 0.15);

    .progress-text-row {
      display: flex;
      align-items: center;

      .progress-label {
        color: #373737;
        font-size: 32rpx;
        font-weight: 400;
      }
      .progress-value {
        margin-left: 8rpx;
        color: #ff3838;
        font-size: 32rpx;
        font-weight: 400;
      }
    }
  }
}
</style>

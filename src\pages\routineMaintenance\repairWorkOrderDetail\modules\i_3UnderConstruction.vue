<template>
  <view class="under-construction-container">
    <!-- 主要内容区域 -->
    <view class="under-construction-content">
      <!-- 工单基础信息卡片 -->
      <view class="construction-card-wrapper">
        <ConstructionCard
          :base-info="repairWorkOrderBaseInfo"
          :detail-info="repairWorkOrderDetail"
          :child-order-list="repairChildWorkOrderList"
          :config-detail="workOrderConfigDetail"
          :page-loading="pageLoading"
          :is-show-bar="true"
        />
      </view>

      <!-- 正常流程：显示子工单详情 -->
      <view v-if="!hasRejectedOrders" class="normal-flow-section">
        <view class="child-work-order-detail-wrap">
          <ChildWorkOrderDetail
            :child-work-order-list="repairChildWorkOrderList"
            :child-order-detail="repairChildWorkOrderDetail"
            :current-page="currentChildOrderPage"
            :show-report-btn="true"
            :repair-work-order-base-info="repairWorkOrderBaseInfo"
            :work-order-config-detail="workOrderConfigDetail"
            mode="carousel"
            @page-change="handleChildOrderPageChange"
            @report="handleReport"
          />

          <!-- 页码切换器：仅在有多个子工单时显示 -->
          <view
            v-if="hasMultipleChildOrders"
            class="page-switcher-wrap"
          >
            <NumberSwitcher
              :current-page="currentChildOrderPage"
              :total-pages="repairChildWorkOrderList.length"
              @change="handleChildOrderPageChange"
            />
          </view>
        </view>
      </view>

      <!-- 驳回流程：显示审核记录和任务完成情况 -->
      <view v-else class="reject-flow-section">
        <ReviewRecordCard
          :check-log-list="verificationContent"
        />
        <TaskCompletionCard
          :complete-info="childWorkOrderCompleteInfo"
          :show-tab-list="true"
          :repair-work-order-base-info="repairWorkOrderBaseInfo"
          :work-order-config-detail="workOrderConfigDetail"
          @go-detail="handleGoToChildWorkOrderDetail"
          @to-re-report="handleReReport"
        />
      </view>
    </view>

    <!-- 底部进度显示区域 -->
    <view class="under-construction-bottom">
      <view class="progress-section">
        <view class="progress-text-row">
          <text class="progress-label">维修任务进度</text>
          <text class="progress-value">
            {{ finishedCount }}/{{ totalChildOrderCount }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import ReviewRecordCard from "../components/card/ReviewRecordCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";

// Props定义
const props = defineProps({
  /** 维修工单基础信息 */
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  /** 维修工单详情（包含审核记录） */
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  /** 子工单作业详情 */
  repairChildWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  /** 子工单完成情况信息 */
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => ({}),
  },
  /** 子工单列表 */
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  /** 工单配置详情信息 */
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  /** 页面加载状态 */
  pageLoading: {
    type: Boolean,
    default: false,
  },
});

// 事件定义
const emit = defineEmits([
  "getRepairChildWorkOrderDetail"
]);

// 响应式数据
const currentChildOrderPage = ref(1);

// 计算属性
/** 已完成的子工单数量 */
const finishedCount = computed(() => {
  return props.repairChildWorkOrderList?.filter(
    (item) => item.workStatus === "4"
  ).length || 0;
});

/** 子工单总数 */
const totalChildOrderCount = computed(() => {
  return props.repairChildWorkOrderList?.length || 0;
});

/** 是否存在被驳回的工单 */
const hasRejectedOrders = computed(() => {
  return !!(props.childWorkOrderCompleteInfo?.reject?.length);
});

/** 是否有多个子工单 */
const hasMultipleChildOrders = computed(() => {
  return totalChildOrderCount.value > 1;
});

/** 审核内容（修正拼写错误） */
const verificationContent = computed(() => {
  return props.repairWorkOrderDetail?.vericaContent || [];
});

// 事件处理方法
/**
 * 处理子工单页面切换
 * @param {number} page - 目标页码
 */
function handleChildOrderPageChange(page) {
  currentChildOrderPage.value = page;
  const targetChildOrder = props.repairChildWorkOrderList[page - 1];
  if (targetChildOrder?.eventId) {
    emit("getRepairChildWorkOrderDetail", targetChildOrder.eventId);
  }
}

/**
 * 处理作业上报
 * @param {Object} item - 子工单信息
 * @param {number} index - 索引
 */
function handleReport(item, index) {
  console.log("作业上报", item, index);

  const finishedNum = finishedCount.value;
  const isLastChild = (totalChildOrderCount.value - finishedNum) === 1;

  const reportUrl = buildReportUrl({
    pageFrom: "startBuild",
    workId: item.parentId,
    childWorkId: item.id,
    saveStatus: item.saveStatus,
    childWorkCode: item.workCode,
    isLastChild
  });

  uni.navigateTo({ url: reportUrl });
}

/**
 * 处理跳转到子工单详情
 * @param {Object} info - 工单信息
 */
function handleGoToChildWorkOrderDetail(info) {
  const workId = props.repairWorkOrderBaseInfo?.id;
  if (!workId || !info?.workId) return;

  const detailUrl = `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index?workId=${workId}&childWorkId=${info.workId}`;
  uni.navigateTo({ url: detailUrl });
}

/**
 * 处理重新上报
 * @param {Object} info - 重新上报信息
 */
function handleReReport(info) {
  const workId = props.repairWorkOrderBaseInfo?.id;
  if (!workId) return;

  const { childWorkId, saveStatus, childWorkCode, flag } = info;
  const reportUrl = buildReportUrl({
    workId,
    childWorkId,
    saveStatus,
    childWorkCode,
    isLastChild: flag
  });

  uni.navigateTo({ url: reportUrl });
}

// 工具函数
/**
 * 构建上报页面URL
 * @param {Object} params - URL参数
 * @returns {string} 完整的URL
 */
function buildReportUrl(params) {
  const {
    pageFrom = "",
    workId,
    childWorkId,
    saveStatus,
    childWorkCode,
    isLastChild
  } = params;

  const baseUrl = "/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/report";
  const queryParams = new URLSearchParams({
    ...(pageFrom && { pageFrom }),
    workId: workId.toString(),
    childWorkId: childWorkId.toString(),
    saveStatus: saveStatus.toString(),
    childWorkCode: childWorkCode.toString(),
    isLastChild: isLastChild.toString()
  });

  return `${baseUrl}?${queryParams.toString()}`;
}
</script>

<style lang="scss" scoped>
// 主容器样式
.under-construction-container {
  background-color: #f4f8ff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 内容区域样式
.under-construction-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;

  // 工单基础信息卡片包装器
  .construction-card-wrapper {
    padding: 0 40rpx;
  }

  // 正常流程区域
  .normal-flow-section {
    .child-work-order-detail-wrap {
      margin-top: 40rpx;
    }

    .page-switcher-wrap {
      margin: 32rpx 0;
    }
  }

  // 驳回流程区域
  .reject-flow-section {
    margin-top: 40rpx;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
  }
}

// 底部进度区域样式
.under-construction-bottom {
  flex-shrink: 0;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;

  .progress-section {
    width: 100%;
    background: #fff;
    padding: 32rpx 40rpx;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(68, 76, 108, 0.15);

    .progress-text-row {
      display: flex;
      align-items: center;

      .progress-label {
        color: #373737;
        font-size: 32rpx;
        font-weight: 400;
      }

      .progress-value {
        margin-left: 8rpx;
        color: #ff3838;
        font-size: 32rpx;
        font-weight: 400;
      }
    }
  }
}
</style>

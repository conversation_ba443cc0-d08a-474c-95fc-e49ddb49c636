<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-11 10:44:01
 * @Description: 
-->
<template>
  <view>
    <uv-popup
      ref="popup"
      mode="bottom"
      round="24"
      @change="change"
      @touchmove.native.prevent="
        (e) => {
          e.preventDefault();
        }
      "
    >
      <view class="content">
        <view class="title">事件上报来源 </view>
        <view class="tabs_box">
          <view
            :class="['tab_item', item.type == curType ? 'tab_active' : '']"
            v-for="(item, index) in types"
            :key="index"
            @click="changeType(item)"
            >{{ item.name }}</view
          >
        </view>
        <view class="title">所属路段 </view>
        <view class="tabs_box road_box">
          <view class="inp">
            <uv-input
              disabled
              placeholder=""
              border="surround"
              v-model="roadInfo.curItemName"
            ></uv-input>
          </view>
          <view class="right_box" @click="changeRoad">
            <view class="btn">去选择</view>
            <view class="icon">
              <uv-icon name="arrow-right" color="#4378ff" size="12"></uv-icon>
            </view>
          </view>
        </view>
        <view class="title">事件对象类型 </view>
        <view class="tabs_box">
          <view
            :class="['tab_item', item.value === curState ? 'tab_active' : '']"
            v-for="(item, index) in dataStatus"
            :key="index"
            @click="changeState(item)"
            >{{ item.label }}</view
          >
        </view>
      </view>
      <view class="btn_box">
        <view class="reset_btn btn" @click="reset">重置</view>
        <view class="comfirm_btn btn" @click="comfirm">筛选</view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { ref, defineEmits } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";

const props = defineProps({
  types: {
    type: Object,
    required: true,
    default: () => {},
  },
  dataStatus: {
    type: Object,
    required: true,
    default: () => {},
  },
  curType: {
    type: [String, Number],
    required: true,
    default: "",
  },
  curState: {
    type: [String, Number],
    required: true,
    default: "",
  },
});
const roadInfo = ref({
  unionKey: "sectionIdLabel",
  curItemId: "",
  curItemName: "",
});
// 切换分类
const changeType = (curItem) => {
  emit("tabPickerCallback", { type: "changeType", val: curItem.type });
};
// 切换分类
const changeRoad = () => {
  const { curItemId, curItemName } = roadInfo.value;
  const url = `/pages/eventHandling/components/SectionList?unionKey=sectionIdLabel&curItemId=${curItemId}&curItemName=${curItemName}`;
  uni.navigateTo({ url });
};
// 切换数据状态
const changeState = (curItem) => {
  emit("tabPickerCallback", { type: "changeState", val: curItem.value });
};
// 重置
const reset = () => {
  roadInfo.value = {
    unionKey: "sectionIdLabel",
    curItemId: "",
    curItemName: "",
  };
  emit("tabPickerCallback", { type: "reset" });
};
// 确定
const emit = defineEmits(["confirmTabs", "tabPickerCallback"]);
const comfirm = () => {
  emit("tabPickerCallback", { type: "confirmTabs" });
};
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
  if (!e.show) {
    emit("tabPickerCallback", { type: "cancelTabs" });
  }
};
onLoad(() => {
  comfirm();
  uni.$on("updateSectionData", (data) => {
    console.log("接收到路段数据:", data);
    const { key, value, label } = data;
    roadInfo.value = { unionKey: key, curItemId: value, curItemName: label };
    emit("tabPickerCallback", { type: "changeRoad", val: value });
  });
});
onUnload(() => {
  uni.$off("updateSectionData");
});
defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx;
  padding-right: 0;
  margin-bottom: 50rpx;
  .title {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
  }
  .tabs_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    .tab_item {
      margin: 0 24rpx 24rpx 0;
      border-radius: 16rpx;
      padding: 6rpx 28rpx;
      box-sizing: border-box;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      background: #f2f2f2;
      color: #8e8e8e;
      line-height: 40rpx;
    }
    .tab_active {
      background: #4378ff;
      color: #fff;
    }
  }
  .road_box {
    padding-right: 40rpx;
    gap: 0 18rpx;
    margin-bottom: 48rpx;
    .inp {
      flex: 1;
    }
    .right_box {
      display: inherit;
      align-items: center;
      .btn {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #4378ff;
        line-height: 32rpx;
      }
      .icon {
      }
    }
  }
}
.btn_box {
  padding: 0 40rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  .btn {
    // width: 256rpx;
    height: 96rpx;
    padding: 20rpx 0;
    box-sizing: border-box;
    text-align: center;
    border-radius: 4rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .reset_btn {
    width: 200rpx;
    background: #dddddd;
    color: #62697b;
  }
  .comfirm_btn {
    width: 430rpx;
    background: #4378ff;
    color: #fff;
  }
}
</style>

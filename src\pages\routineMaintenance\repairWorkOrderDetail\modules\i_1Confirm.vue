<template>
  <view class="work-order-confirm-container">
    <view class="work-order-confirm-content">
      <WorkOrderWrapCard :base-info="workOrderBaseInfo">
        <template #header>
          <view class="work-order-confirm-title">
            <view class="work-order-confirm-title-bar">
              <view class="title-indicator"></view>
              <view class="title-text">维修工单接受</view>
            </view>
          </view>
        </template>
        <template #body>
          <!-- 基础信息 -->
          <view
            v-for="item in baseInfoDisplayList"
            :key="item.key"
            class="work-order-info-row"
          >
            <view class="work-order-info-label">{{ item.label }}</view>
            <view class="work-order-info-value">
              {{ item.value || "-" }}
            </view>
          </view>
          <!-- 子工单详情 -->
          <view class="child-work-order-section">
            <ChildWorkOrderDetail
              :child-work-order-list="childWorkOrderList"
              :child-order-detail="childWorkOrderDetail"
              :current-page="currentChildOrderPage"
              @page-change="handleChildOrderPageChange"
            />
            <!-- 页码切换器 -->
            <view
              v-if="childWorkOrderList.length > 1"
              class="page-switcher-container"
            >
              <NumberSwitcher
                :current-page="currentChildOrderPage"
                :total-pages="childWorkOrderList.length"
                @change="handleChildOrderPageChange"
              />
            </view>
          </view>
        </template>
      </WorkOrderWrapCard>
    </view>
    <view class="footer-action-area" v-if="hasConfirmPermission">
      <button class="confirm-button" @click="handleConfirmOrder">
        确认工单
      </button>
    </view>
  </view>
</template>
<script setup>
// ==================== 导入依赖 ====================
import { ref, computed, watch } from "vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import WorkOrderWrapCard from "../components/card/WorkOrderWrapCard.vue";
import { RoutineMaintenanceService } from "@/service";

// ==================== 组件属性定义 ====================
/**
 * 组件属性接口
 * @typedef {Object} Props
 * @property {Object} repairWorkOrderBaseInfo - 维修工单基础信息
 * @property {Array} repairChildWorkOrderList - 子工单列表
 */
const props = defineProps({
  /** 维修工单基础信息 */
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  /** 子工单列表 */
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  }
});

// ==================== 响应式数据 ====================
/** 当前子工单页码 */
const currentChildOrderPage = ref(1);

/** 当前子工单详情数据 */
const childWorkOrderDetail = ref({});

// ==================== 计算属性 ====================
/** 工单基础信息 */
const workOrderBaseInfo = computed(() => props.repairWorkOrderBaseInfo || {});

/** 子工单列表 */
const childWorkOrderList = computed(() => props.repairChildWorkOrderList || []);

/** 是否有确认工单权限 */
const hasConfirmPermission = computed(() => {
  return workOrderBaseInfo.value?.resourceList?.includes("dailyRepairConfirm") || false;
});

/** 基础信息展示列表 */
const baseInfoDisplayList = computed(() => [
  {
    key: "createTime",
    label: "接收时间：",
    value: formatDateTime(workOrderBaseInfo.value?.createTime),
  },
  {
    key: "createUserName",
    label: "工单来源：",
    value: workOrderBaseInfo.value?.createUserName,
  },
]);

// ==================== 工具函数 ====================
/**
 * 格式化日期时间
 * @param {string} dateTime - ISO格式的日期时间字符串
 * @returns {string} 格式化后的日期时间字符串 (YYYY-MM-DD HH:mm)
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  return dateTime.replace(/T/, " ").substring(0, 16);
};

// ==================== API 调用方法 ====================
/**
 * 获取子工单详情
 * @param {string} eventId - 子工单事件ID
 */
const fetchChildWorkOrderDetail = async (eventId) => {
  if (!eventId) return;

  try {
    const { data } = await RoutineMaintenanceService.getInspectEventDetail(eventId);
    childWorkOrderDetail.value = data;
  } catch (error) {
    console.error("获取子工单详情失败:", error);
  }
};

// ==================== 事件处理方法 ====================
/**
 * 处理子工单页码切换
 * @param {number} pageNumber - 目标页码 (1-based)
 */
const handleChildOrderPageChange = (pageNumber) => {
  currentChildOrderPage.value = pageNumber;
  const targetChildOrder = childWorkOrderList.value[pageNumber - 1];
  fetchChildWorkOrderDetail(targetChildOrder?.eventId);
};

/**
 * 处理确认工单操作
 * 跳转到工单计划计量页面
 */
const handleConfirmOrder = () => {
  const workOrderId = workOrderBaseInfo.value.id;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/confirmOrder?workId=${workOrderId}`,
  });
};

// ==================== 监听器 ====================
/**
 * 监听子工单列表变化，自动加载第一个子工单详情
 */
watch(
  () => childWorkOrderList.value,
  (newChildOrderList) => {
    if (newChildOrderList && newChildOrderList.length > 0) {
      // 重置到第一页并加载详情
      currentChildOrderPage.value = 1;
      fetchChildWorkOrderDetail(newChildOrderList[0]?.eventId);
    }
  },
  { immediate: true }
);
</script>
<style lang="scss">
@import "../common.scss";

.work-order-confirm-container {
  background-color: #f4f8ff;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .work-order-confirm-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    padding: 0 40rpx;
    box-sizing: border-box;
  }

  .work-order-confirm-title {
    margin-top: 28rpx;
    display: flex;
    align-items: center;

    .work-order-confirm-title-bar {
      display: flex;
      align-items: center;

      .title-indicator {
        margin-right: 8rpx;
        width: 6rpx;
        height: 28rpx;
        background: $primary-color;
        border-radius: 4rpx;
      }

      .title-text {
        font-family: $font-family;
        font-weight: 500;
        font-size: 32rpx;
        color: $text-secondary;
      }
    }
  }

  .work-order-info-row {
    display: flex;
    margin-top: 24rpx;

    .work-order-info-label {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-muted;
      line-height: 40rpx;
    }

    .work-order-info-value {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-primary;
      line-height: 40rpx;
    }
  }

  .child-work-order-section {
    padding: 40rpx 0;
  }

  .page-switcher-container {
    margin-top: 32rpx;
    padding: 0 28rpx;
  }

  .footer-action-area {
    height: 160rpx;
    margin-top: 24rpx;
    flex-shrink: 0;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent;

    .confirm-button {
      width: 92vw;
      height: 84rpx;
      line-height: 84rpx;
      border-radius: 8rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600;
      background: #4378ff;
      color: #ffffff;
      font-size: 40rpx;
      transition: all 0.2s ease;
      box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);

      &:active {
        background: #3366ee;
        transform: scale(0.99);
        transition: all 0.1s ease;
      }
    }
  }
}
</style>

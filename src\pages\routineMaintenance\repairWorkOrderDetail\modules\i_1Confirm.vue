<template>
  <view class="work-order-confirm-container">
    <!-- 主要内容区域 -->
    <view class="work-order-confirm-content">
      <WorkOrderWrapCard :base-info="workOrderBaseInfo">
        <!-- 标题区域 -->
        <template #header>
          <view class="confirm-title-section">
            <view class="title-bar">
              <view class="title-indicator" />
              <view class="title-text">维修工单接受</view>
            </view>
          </view>
        </template>

        <!-- 内容区域 -->
        <template #body>
          <!-- 基础信息展示 -->
          <view class="basic-info-section">
            <view
              v-for="item in baseInfoDisplayList"
              :key="item.key"
              class="info-row"
            >
              <view class="info-label">{{ item.label }}</view>
              <view class="info-value">{{ item.value || "-" }}</view>
            </view>
          </view>

          <!-- 子工单详情区域 -->
          <view class="child-work-order-section">
            <ChildWorkOrderDetail
              :child-work-order-list="childWorkOrderList"
              :child-order-detail="childWorkOrderDetail"
              :current-page="currentChildOrderPage"
              :loading="isLoadingChildOrderDetail"
              @page-change="handleChildOrderPageChange"
            />

            <!-- 页码切换器：仅在有多个子工单时显示 -->
            <view
              v-if="hasMultipleChildOrders"
              class="page-switcher-container"
            >
              <NumberSwitcher
                :current-page="currentChildOrderPage"
                :total-pages="totalChildOrderPages"
                @change="handleChildOrderPageChange"
              />
            </view>
          </view>
        </template>
      </WorkOrderWrapCard>
    </view>

    <!-- 底部操作按钮区域 -->
    <view v-if="hasConfirmPermission" class="footer-action-area">
      <button
        class="confirm-button"
        :disabled="isConfirmButtonDisabled"
        @click="handleConfirmOrder"
      >
        <text class="button-text">确认工单</text>
      </button>
    </view>
  </view>
</template>
<script setup>
// ==================== 导入依赖 ====================
import { ref, computed, watch } from "vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import WorkOrderWrapCard from "../components/card/WorkOrderWrapCard.vue";
import { RoutineMaintenanceService } from "@/service";

// ==================== 常量定义 ====================
/** 页面路由常量 */
const ROUTES = {
  CONFIRM_ORDER: "/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/confirmOrder",
};

/** 权限资源常量 */
const PERMISSIONS = {
  DAILY_REPAIR_CONFIRM: "dailyRepairConfirm",
};

/** 日期时间格式常量 */
const DATE_FORMAT = {
  DISPLAY: "YYYY-MM-DD HH:mm",
  ISO_SUBSTRING_LENGTH: 16,
};

// ==================== Props定义 ====================
const props = defineProps({
  /** 维修工单基础信息 */
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  /** 子工单列表 */
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
});

// ==================== 响应式数据 ====================
/** 当前子工单页码（1-based） */
const currentChildOrderPage = ref(1);

/** 当前子工单详情数据 */
const childWorkOrderDetail = ref({});

/** 子工单详情加载状态 */
const isLoadingChildOrderDetail = ref(false);

// ==================== 计算属性 ====================
/** 规范化的工单基础信息 */
const workOrderBaseInfo = computed(() => props.repairWorkOrderBaseInfo ?? {});

/** 规范化的子工单列表 */
const childWorkOrderList = computed(() => props.repairChildWorkOrderList ?? []);

/** 子工单总页数 */
const totalChildOrderPages = computed(() => childWorkOrderList.value.length);

/** 是否有多个子工单 */
const hasMultipleChildOrders = computed(() => totalChildOrderPages.value > 1);

/** 是否有确认工单权限 */
const hasConfirmPermission = computed(() => {
  return workOrderBaseInfo.value?.resourceList?.includes(PERMISSIONS.DAILY_REPAIR_CONFIRM) ?? false;
});

/** 确认按钮是否禁用 */
const isConfirmButtonDisabled = computed(() => {
  return !workOrderBaseInfo.value?.id || isLoadingChildOrderDetail.value;
});

/** 基础信息展示列表 */
const baseInfoDisplayList = computed(() => [
  {
    key: "createTime",
    label: "接收时间：",
    value: formatDateTime(workOrderBaseInfo.value?.createTime),
  },
  {
    key: "createUserName",
    label: "工单来源：",
    value: workOrderBaseInfo.value?.createUserName,
  },
]);

// ==================== 工具函数 ====================
/**
 * 格式化日期时间
 * @param {string} dateTime - ISO格式的日期时间字符串
 * @returns {string} 格式化后的日期时间字符串 (YYYY-MM-DD HH:mm)
 */
function formatDateTime(dateTime) {
  if (!dateTime || typeof dateTime !== 'string') return "";
  return dateTime.replace(/T/, " ").substring(0, DATE_FORMAT.ISO_SUBSTRING_LENGTH);
}

/**
 * 安全的页面跳转
 * @param {string} url - 跳转URL
 * @param {Object} options - 跳转选项
 */
function safeNavigateTo(url, options = {}) {
  if (!url) {
    console.warn("导航URL不能为空");
    return;
  }

  try {
    uni.navigateTo({
      url,
      ...options,
      fail: (error) => {
        console.error("页面跳转失败:", error);
        uni.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      }
    });
  } catch (error) {
    console.error("页面跳转异常:", error);
  }
}

// ==================== API 调用方法 ====================
/**
 * 获取子工单详情
 * @param {string} eventId - 子工单事件ID
 */
async function fetchChildWorkOrderDetail(eventId) {
  if (!eventId) {
    console.warn("子工单事件ID不能为空");
    return;
  }

  try {
    isLoadingChildOrderDetail.value = true;
    const { data } = await RoutineMaintenanceService.getInspectEventDetail(eventId);
    childWorkOrderDetail.value = data ?? {};
  } catch (error) {
    console.error("获取子工单详情失败:", error);
    uni.showToast({
      title: "获取子工单详情失败",
      icon: "none",
    });
    childWorkOrderDetail.value = {};
  } finally {
    isLoadingChildOrderDetail.value = false;
  }
}

// ==================== 事件处理方法 ====================
/**
 * 处理子工单页码切换
 * @param {number} pageNumber - 目标页码 (1-based)
 */
function handleChildOrderPageChange(pageNumber) {
  if (pageNumber < 1 || pageNumber > totalChildOrderPages.value) {
    console.warn("页码超出范围:", pageNumber);
    return;
  }

  currentChildOrderPage.value = pageNumber;
  const targetChildOrder = childWorkOrderList.value[pageNumber - 1];

  if (targetChildOrder?.eventId) {
    fetchChildWorkOrderDetail(targetChildOrder.eventId);
  }
}

/**
 * 处理确认工单操作
 * 跳转到工单确认页面
 */
function handleConfirmOrder() {
  const workOrderId = workOrderBaseInfo.value?.id;

  if (!workOrderId) {
    uni.showToast({
      title: "工单信息无效",
      icon: "none",
    });
    return;
  }

  const confirmUrl = `${ROUTES.CONFIRM_ORDER}?workId=${workOrderId}`;
  safeNavigateTo(confirmUrl);
}

// ==================== 监听器 ====================
/**
 * 监听子工单列表变化，自动加载第一个子工单详情
 */
watch(
  () => childWorkOrderList.value,
  (newChildOrderList, oldChildOrderList) => {
    // 避免不必要的重复加载
    if (newChildOrderList === oldChildOrderList) return;

    if (newChildOrderList?.length > 0) {
      // 重置到第一页并加载详情
      currentChildOrderPage.value = 1;
      const firstChildOrder = newChildOrderList[0];

      if (firstChildOrder?.eventId) {
        fetchChildWorkOrderDetail(firstChildOrder.eventId);
      }
    } else {
      // 清空子工单详情
      childWorkOrderDetail.value = {};
      currentChildOrderPage.value = 1;
    }
  },
  { immediate: true, deep: false }
);
</script>
<style lang="scss" scoped>
@import "../common.scss";

// ==================== 主容器样式 ====================
.work-order-confirm-container {
  background-color: #f4f8ff;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;

  // ==================== 内容区域样式 ====================
  .work-order-confirm-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    padding: 0 40rpx 180rpx 40rpx; // 为底部按钮预留空间
    box-sizing: border-box;
  }

  // ==================== 标题区域样式 ====================
  .confirm-title-section {
    margin-top: 28rpx;
    display: flex;
    align-items: center;

    .title-bar {
      display: flex;
      align-items: center;

      .title-indicator {
        margin-right: 8rpx;
        width: 6rpx;
        height: 28rpx;
        background: $primary-color;
        border-radius: 4rpx;
        flex-shrink: 0;
      }

      .title-text {
        font-family: $font-family;
        font-weight: 500;
        font-size: 32rpx;
        color: $text-secondary;
        line-height: 1.2;
      }
    }
  }

  // ==================== 基础信息区域样式 ====================
  .basic-info-section {
    margin-top: 24rpx;
  }

  .info-row {
    display: flex;
    align-items: flex-start;
    margin-top: 24rpx;

    &:first-child {
      margin-top: 0;
    }

    .info-label {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-muted;
      line-height: 40rpx;
      flex-shrink: 0;
    }

    .info-value {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-primary;
      line-height: 40rpx;
      word-break: break-all;
    }
  }

  // ==================== 子工单区域样式 ====================
  .child-work-order-section {
    padding: 40rpx 0;
  }

  .page-switcher-container {
    margin-top: 32rpx;
    padding: 0 28rpx;
  }

  // ==================== 底部操作区域样式 ====================
  .footer-action-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 160rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    padding: 32rpx 40rpx;
    box-sizing: border-box;

    // 添加上边框阴影
    box-shadow: 0 -4rpx 20rpx rgba(68, 76, 108, 0.15);

    // 确保在最上层
    z-index: 100;

    .confirm-button {
      width: 100%;
      max-width: 600rpx;
      height: 96rpx;
      border: none;
      border-radius: 12rpx;
      font-family: "PingFang SC", "PingFang SC", sans-serif;
      font-weight: 600;
      font-size: 40rpx;
      background: linear-gradient(135deg, #4378ff 0%, #3366ee 100%);
      color: #ffffff;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      // 过渡效果
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4rpx 16rpx rgba(67, 120, 255, 0.3);

      .button-text {
        position: relative;
        z-index: 2;
      }

      // 按钮波纹效果
      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
        z-index: 1;
      }

      &:hover {
        background: linear-gradient(135deg, #3366ee 0%, #2255dd 100%);
        transform: translateY(-2rpx);
        box-shadow: 0 6rpx 20rpx rgba(67, 120, 255, 0.4);
      }

      &:active {
        background: linear-gradient(135deg, #2255dd 0%, #1144cc 100%);
        transform: translateY(0) scale(0.98);

        &::before {
          width: 200%;
          height: 200%;
        }
      }

      // 禁用状态
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: #cccccc;
        transform: none !important;
        box-shadow: none !important;

        &:hover,
        &:active {
          transform: none !important;
          box-shadow: none !important;
          background: #cccccc !important;
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media screen and (max-width: 750rpx) {
  .work-order-confirm-container {
    .footer-action-area {
      padding: 24rpx 32rpx;

      .confirm-button {
        height: 88rpx;
        font-size: 36rpx;
        border-radius: 10rpx;
      }
    }
  }
}
</style>

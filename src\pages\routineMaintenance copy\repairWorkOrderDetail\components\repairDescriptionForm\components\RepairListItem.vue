<template>
  <view class="repair-support-item" @click="$emit('click')">
    <!-- 头像区域 (仅人员显示) -->
    <view v-if="showAvatar" class="repair-support-avatar">
      {{ avatarText }}
    </view>

    <!-- 左侧信息 -->
    <view class="repair-support-info-left">
      <view class="repair-support-name">{{ title }}</view>
      <view v-if="subtitle" class="repair-support-dept">{{ subtitle }}</view>
    </view>

    <!-- 右侧信息 -->
    <view class="repair-support-info-right">
      <view class="repair-support-unitPrice">{{ priceText }}</view>
      <view class="repair-support-dept" :class="{ 'no-count': !countValue }">
        <image class="count" :src="countValue ? count_icon : noCount_icon" />
        {{ countValue || "-" }}
      </view>
    </view>

    <!-- 箭头 -->
    <image class="more-arrow" src="@/static/icon/gary-down.png" />
  </view>
</template>

<script setup>
import count_icon from "@/static/icon/count.png";
import noCount_icon from "@/static/icon/noCount.png";
defineProps({
  // 是否显示头像
  showAvatar: {
    type: Boolean,
    default: false,
  },
  // 头像文字
  avatarText: {
    type: String,
    default: "",
  },
  // 主标题
  title: {
    type: String,
    required: true,
  },
  // 副标题
  subtitle: {
    type: String,
    default: "",
  },
  // 价格文本
  priceText: {
    type: String,
    required: true,
  },
  // 数量值
  countValue: {
    type: [String, Number],
    default: "",
  },
});

defineEmits(["click"]);
</script>

<style lang="scss" scoped>
.repair-support-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 2rpx solid #f2f2f2;
  margin: 0;
  padding: 12rpx 0;

  &:first-child {
    border-top: none;
  }
  &:last-child {
    border-bottom: none;
  }

  .repair-support-info-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 8rpx 0;
    gap: 8rpx;
    min-width: 0;
  }

  .repair-support-info-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding: 8rpx 0;
    gap: 8rpx;
    min-width: 0;
  }

  .repair-support-avatar {
    width: 76rpx;
    height: 76rpx;
    background: #4378ff;
    color: #fff;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 500;
    margin: 0 16rpx 0 0;
    flex-shrink: 0;
  }

  .repair-support-name {
    width: 100%;
    font-size: 32rpx;
    color: #373737;
    font-weight: 500;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .repair-support-dept {
    font-size: 28rpx;
    color: #a0a0a0;
    line-height: 1.2;
    word-break: break-all;
    display: flex;
    align-items: center;
  }

  .repair-support-unitPrice {
    color: #a0a0a0;
    font-size: 28rpx;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .more-arrow {
    width: 32rpx;
    height: 32rpx;
    margin-left: 16rpx;
    flex-shrink: 0;
    margin-right: 0;
  }

  .count {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
    margin-top: 4rpx;
    flex-shrink: 0;
  }

  .no-count {
    color: #f06060;
  }
}
</style>

<template>
  <view v-if="mode === 'carousel' && childWorkOrderList.length > 0" class="child-work-order-card">
    <view 
      class="carousel-container"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <view 
        class="carousel-wrapper"
        :style="wrapperStyle"
      >
        <view 
          v-for="(item, index) in childWorkOrderList" 
          :key="index"
          class="carousel-item"
        >
          <ChildWorkOrderCard
            :childWorkOrderDetail="childOrderDetail"
            :active="currentPage === index + 1"
            :showReportBtn="showReportBtn"
            :repairWorkOrderBaseInfo="repairWorkOrderBaseInfo"
            :workOrderConfigDetail="workOrderConfigDetail"
            @report="$emit('report', item, index)"
          />
        </view>
      </view>
    </view>
  </view>
  <view v-else-if="mode === 'static' && childWorkOrderList.length > 0" class="child-work-order-card static-mode">
    <view class="static-content">
      <ChildWorkOrderCard
        :childWorkOrderDetail="childOrderDetail"
        :active="true"
        :showReportBtn="showReportBtn"
        :repairWorkOrderBaseInfo="repairWorkOrderBaseInfo"
        :workOrderConfigDetail="workOrderConfigDetail"
        @report="$emit('report', childOrderDetail, currentPage - 1)"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import ChildWorkOrderCard from "./card/ChildWorkOrderCard.vue";

const CARD_WIDTH = 84; // 单位vw
const CARD_MARGIN = 2; // 单位vw，两侧各留2vw
const SIDE_PADDING = 6; // 单位vw，两侧padding保证居中

const props = defineProps({
  childWorkOrderList: {
    type: Array,
    default: () => [],
  },
  childOrderDetail: {
    type: Object,
    default: () => {},
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  mode: {
    type: String,
    default: 'static', // 'carousel' or 'static'
  },
  showReportBtn: {
    type: Boolean,
    default: false
  },
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 维修配置信息
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["addressClick", "pageChange", "report"]);

// 触摸相关状态
const touchStartX = ref(0);
const touchStartY = ref(0);
const touchEndX = ref(0);
const touchEndY = ref(0);
const minSwipeDistance = 50; // 最小滑动距离

// 计算wrapper的transform
const wrapperStyle = computed(() => {
  const itemWidth = CARD_WIDTH + CARD_MARGIN * 2;
  const offset = (props.currentPage - 1) * itemWidth;
  return {
    paddingLeft: `${SIDE_PADDING}vw`,
    paddingRight: `${SIDE_PADDING}vw`,
    transform: `translateX(-${offset}vw)` ,
    transition: 'transform 0.3s cubic-bezier(.4,0,.2,1)'
  };
});

// 触摸开始
const handleTouchStart = (e) => {
  touchStartX.value = e.touches[0].clientX;
  touchStartY.value = e.touches[0].clientY;
};

// 触摸移动
const handleTouchMove = (e) => {
  touchEndX.value = e.touches[0].clientX;
  touchEndY.value = e.touches[0].clientY;
};

// 触摸结束
const handleTouchEnd = () => {
  const deltaX = touchStartX.value - touchEndX.value;
  const deltaY = touchStartY.value - touchEndY.value;
  
  // 判断是否为水平滑动（水平距离大于垂直距离）
  if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
    if (deltaX > 0) {
      // 向左滑动，下一页
      if (props.currentPage < props.childWorkOrderList.length) {
        emit("pageChange", props.currentPage + 1);
      }
    } else {
      // 向右滑动，上一页
      if (props.currentPage > 1) {
        emit("pageChange", props.currentPage - 1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
$primary-color: #4378ff;

.carousel-container {
  width: 100vw;
  overflow: visible;
  overflow-x: hidden;
  position: relative;
  touch-action: pan-y; // 允许垂直滚动，防止浏览器默认横向滑动行为影响
}

.carousel-wrapper {
  display: flex;
  align-items: center;
}

.carousel-item {
  flex-shrink: 0;
  width: 84vw;
  margin: 0 2vw;
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  position: relative;
}
</style>

<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-09-11 11:30:50
 * @Description: 
-->
<template>
  <view class="container">
    <scroll-view scroll-y="true" class="content">
      <view class="work_code_box">
        <view class="left_title">工单编码</view>
        <view class="code_content">{{ workCode }}</view>
        <view class="copy_btn" @click="onCopy">复制</view>
      </view>
      <ylg-auto-form
        ref="autoFormRef"
        :isAllDisabled="false"
        :isFromDetail="false"
        :formConfig="formConfig"
        :formData="formData"
        :rules="rules"
        :btnLoading="btnLoading"
        :labelStyle="labelStyle"
        :formItemStyle="formItemStyle"
        :placeholderStyle="placeholderStyle"
        cancelBtnText="暂 存"
        confirmBtnText="上 报"
        @onChange="onFormInpChange"
        @onSubmit="onFormSubmit"
      ></ylg-auto-form>
      <view class="black" style="height: 100rpx"></view>
      <uv-toast ref="toast"></uv-toast>
    </scroll-view>
    <ylg-temporary-modal
      ref="temporaryModal"
      title="退出前是否需要暂存当前进度"
      notice="之后可选择该工单继续补录"
      btnText="暂存当前进度"
      :btnLoading="btnLoading"
      @onModalCallback="onBackModal"
    ></ylg-temporary-modal>
  </view>
</template>
<script setup>
import { computed, ref, reactive } from "vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import { getSysteminfo, uploadFilePromise } from "@/utils";
// import ylgAutoForm from "@/components/ylg-auto-form.vue";
// import ylgTemporaryModal from "@/components/ylg-temporary-modal.vue";
import { RoutineMaintenanceService } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { showImg } from "@/utils";

// 获取手机底部安全距离
const systemBottomSafeArea = `${
  Number(getSysteminfo().bottomSafeArea) * 2 || 40
}rpx`;
console.log("手机底部安全距离", systemBottomSafeArea);

// form表单样式配置
const labelStyle = reactive({
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "33rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
});
const placeholderStyle = ref("color: #C1C1C1");

let workId = ref("");
let childWorkId = ref("");
let workCode = ref("");
let saveStatus = ref("");
let pageFrom = ref("");
let isLastChild = ref(false); // 当前上报的是否为最后一条子工单
onLoad((options) => {
  console.log("作业上报表单页", options);
  workId.value = options.workId;
  pageFrom.value = options?.pageFrom === "undefined" ? "" : options?.pageFrom;
  childWorkId.value = options.childWorkId;
  workCode.value = options.childWorkCode;
  isLastChild.value = options.isLastChild === "false" ? false : true;
  saveStatus.value = options.saveStatus; // null:没有上报过；'1':保存了的；'0':暂存了的
  if (["0", "1"].includes(saveStatus.value)) {
    getDetail();
  }
});

let temporaryModal = ref(null);
onBackPress((backOptions) => {
  if (backOptions.from === "backbutton") {
    temporaryModal.value.open();
    return true;
  } else if (backOptions.from === "navigateBack") {
    return false;
  }
});
// 返回上一页modal组件回调事件
const onBackModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      temporaryModal.value.close();
      break;
    case "onNotSave":
      temporaryModal.value.close();
      uni.navigateBack({
        delta: 1,
      });
      break;
    case "onTemporaryDraft":
      onFormSubmit({ data: formData.value, status: 0 });
      break;
    default:
      break;
  }
};

// 表单数据配置
let formData = ref({
  // 项目id
  projectId: projectInfo.projectId,
  // 施工情况说明
  conditionRemark: "",

  // 施工前照片
  buildBeforePathArr: [],
  // 施工中照片
  buildPathArr: [],
  // 施工后照片
  buildAfterPathArr: [],
});
const rules = computed(() => {
  return {
    conditionRemark: {
      type: "string",
      required: true,
      message: "请输入施工情况说明",
      trigger: ["blur"],
    },
    buildBeforePathArr: [
      {
        validator: (rule, value, callback) => {
          return value.length == 1;
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
    buildPathArr: [
      {
        validator: (rule, value, callback) => {
          return value.length == 1;
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
    buildAfterPathArr: [
      {
        validator: (rule, value, callback) => {
          return value.length == 1;
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
  };
});
const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "textarea",
          label: "施工情况说明",
          maxlen: 150,
          placeholder: "请输入",
          unionKey: "conditionRemark",
        },
        {
          type: "upload",
          label: "施工前照片",
          fileList: ref(formData.value.buildBeforePathArr),
          formItemHeight: "244rpx",
          maxCount: 1,
          unionKey: "buildBeforePathArr",
        },
        {
          type: "upload",
          label: "施工中照片",
          fileList: ref(formData.value.buildPathArr),
          formItemHeight: "244rpx",
          maxCount: 1,
          unionKey: "buildPathArr",
        },
        {
          type: "upload",
          label: "施工后照片",
          fileList: ref(formData.value.buildAfterPathArr),
          formItemHeight: "244rpx",
          maxCount: 1,
          unionKey: "buildAfterPathArr",
        },
      ],
    },
  ];
});

// 测试数据回显
let detailId = ref("");
const getDetail = async () => {
  try {
    let params = {
      workId: workId.value,
      childWorkId: childWorkId.value,
    };
    let { code, data } =
      await RoutineMaintenanceService.childOrderDetail(params);
    if (code == 200) {
      console.log("获取子工单详情", data);
      detailId.value = data.id;
      formData.value.conditionRemark = data.conditionRemark;
      data.buildBeforePath &&
        (formData.value.buildBeforePathArr = [
          { url: showImg(data.buildBeforePath) },
        ]);
      data.buildPath &&
        (formData.value.buildPathArr = [{ url: showImg(data.buildPath) }]);
      data.buildAfterPath &&
        (formData.value.buildAfterPathArr = [
          { url: showImg(data.buildAfterPath) },
        ]);
      console.log("详情回显", formData.value);
    }
  } catch (error) {
    console.log("获取子工单详情失败", error);
  }
};

// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(formData.value, unionKey.split("."), val);
  console.log("onFormInpChange", formData.value);
};
const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
  console.log("setNestedValue", formData.value);
};
const onCopy = () => {
  console.log("复制");
  uni.setClipboardData({
    data: workCode.value,
    success: function () {
      uni.showToast({
        title: "复制成功！",
        icon: "none",
      });
    },
  });
};

// form表单提交回调
let autoFormRef = ref(null);
let toast = ref(null);
let btnLoading = ref(""); // '0'-暂存按钮；'1'-提交按钮
const onFormSubmit = async ({ data, status }) => {
  console.log("作业上报", data, status);
  // 暂存时校验是否有上传照片
  if (status == 0) {
    autoFormRef.value.formRef.clearValidate();
  }
  if (
    !data.buildBeforePathArr.length &&
    !data.buildPathArr.length &&
    !data.buildAfterPathArr.length
  ) {
    toast.value.show({
      type: "warning",
      message: `最少需要上传一张照片才可${status == 0 ? "暂存" : "上报"}`,
    });
    return;
  }

  // 上报
  try {
    btnLoading.value = String(status);
    let params = {};
    // 父工单id
    params.workId = workId.value;
    // 子工单id
    params.childWorkId = childWorkId.value;
    params.id = detailId.value; // 修改数据时要传id；
    // 草稿/正式
    params.saveStatus = status;
    // 情况说明
    params.conditionRemark = data.conditionRemark;
    // 照片
    if (data?.buildBeforePathArr?.length) {
      let startIndex = data.buildBeforePathArr[0]?.url.indexOf("/file");
      if (startIndex !== -1) {
        params.buildBeforePath =
          data.buildBeforePathArr[0]?.url.substring(startIndex);
      } else {
        console.log("未找到 '/file' 子字符串");
      }
    } else {
      params.buildBeforePath = "";
    }
    if (data?.buildPathArr?.length) {
      let startIndex = data.buildPathArr[0]?.url.indexOf("/file");
      if (startIndex !== -1) {
        params.buildPath = data.buildPathArr[0]?.url.substring(startIndex);
      } else {
        console.log("未找到 '/file' 子字符串");
      }
    } else {
      params.buildPath = "";
    }
    if (data?.buildAfterPathArr?.length) {
      let startIndex = data.buildAfterPathArr[0]?.url.indexOf("/file");
      if (startIndex !== -1) {
        params.buildAfterPath =
          data.buildAfterPathArr[0]?.url.substring(startIndex);
      } else {
        console.log("未找到 '/file' 子字符串");
      }
    } else {
      params.buildAfterPath = "";
    }

    console.log(
      "上报作业",
      status,
      params,
      isLastChild.value,
      typeof isLastChild.value
    );
    // return;
    let { code, data: resData } =
      await RoutineMaintenanceService.reportBuild(params);
    btnLoading.value = "";
    console.log("res", status, resData);
    if (code == 200) {
      if (isLastChild.value && status != 0) {
        toast.value.show({
          type: "success",
          message: `施工作业结束`,
          complete() {
            uni.$emit("refreshWorkOrderBaseInfo");
            uni.navigateBack();
          },
        });
      } else {
        toast.value.show({
          type: "success",
          message: `作业${status == 0 ? "暂存" : "上报"}成功`,
          complete() {
            uni.$emit("refreshChildOrderDetailList");
            uni.navigateBack();
          },
        });
      }
    }
  } catch (error) {
    console.log("作业上报失败", error, formData.value);
    btnLoading.value = "";
  }
};
</script>

<style lang="scss" scoped>
.work_code_box {
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  .left_title {
    width: 252rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
  .code_content {
    font-weight: 400;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
  .copy_btn {
    margin-left: 28rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 600;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 40rpx;
  }
}
</style>

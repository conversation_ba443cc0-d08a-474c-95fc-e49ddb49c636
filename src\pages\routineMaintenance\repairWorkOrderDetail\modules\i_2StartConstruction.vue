<template>
  <view class="start-construction-container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <ConstructionCard
        :base-info="repairWorkOrderBaseInfo"
        :detail-info="repairWorkOrderDetail"
        :child-order-list="repairChildWorkOrderList"
        :config-detail="workOrderConfigDetail"
        :page-loading="pageLoading"
      />
    </view>

    <!-- 底部操作按钮区域 -->
    <view class="bottom-actions">
      <uv-button
        v-if="hasPermission"
        :loading="isStartButtonLoading"
        :custom-style="startButtonStyle"
        :custom-text-style="startButtonTextStyle"
        text="开始施工"
        @click="handleStartConstruction"
      />
    </view>

    <!-- 开始施工确认弹窗 -->
    <BuildModal
      ref="buildModalRef"
      :current-time="currentTime"
      :current-remark="remarkContent"
      :current-address="currentPosition.address"
      :is-cancel-disabled="isCancelDisabled"
      :is-refresh-loading="isLocationRefreshing"
      @on-build-callback="handleBuildModalCallback"
    />

    <!-- 备注编辑弹窗 -->
    <RemarkModal
      ref="remarkModalRef"
      :current-remark="remarkContent"
      @on-remark-callback="handleRemarkCallback"
    />
  </view>
</template>
<script setup>
import { ref, reactive, computed } from "vue";
import dayjs from "dayjs";
import BuildModal from "../components/modal/BuildModal.vue";
import RemarkModal from "@/components/ylg-remark-modal.vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import { getCurLocation, reverseGeocode } from "@/utils/location";
import { RoutineMaintenanceService } from "@/service";
import { useUserStore } from "@/store/user";

// Props定义
const props = defineProps({
  /** 维修工单基础信息 */
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  /** 维修工单详情信息 */
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  /** 子工单列表 */
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  /** 工单配置详情信息 */
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  /** 页面加载状态 */
  pageLoading: {
    type: Boolean,
    default: false,
  },
});

// Store实例
const userStore = useUserStore();

// 计算属性
/** 检查用户是否有开始施工的权限 */
const hasPermission = computed(() => {
  // 检查资源权限
  const hasResourcePermission = props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairWork") ?? false;

  // 检查当前用户是否在工单用户列表中
  const currentUserId = userStore.id;
  const isUserInWorkOrder = props.workOrderConfigDetail?.users?.some(
    (user) => user.relevancyId === currentUserId
  ) ?? false;

  return hasResourcePermission && isUserInWorkOrder;
});

// 响应式数据
/** 开始施工按钮加载状态 */
const isStartButtonLoading = ref(false);

/** 开始施工按钮样式配置 */
const startButtonStyle = {
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378ff",
  color: "#ffffff",
};

/** 开始施工按钮文字样式配置 */
const startButtonTextStyle = {
  fontSize: "40rpx",
};

/** 施工确认弹窗引用 */
const buildModalRef = ref(null);

/** 备注弹窗引用 */
const remarkModalRef = ref(null);

/** 当前时间 */
const currentTime = ref("");

/** 当前位置信息 */
const currentPosition = reactive({
  longitude: "",
  latitude: "",
  address: "",
});

/** 备注内容 */
const remarkContent = ref("");

/** 取消按钮禁用状态 */
const isCancelDisabled = ref(false);

/** 位置刷新加载状态 */
const isLocationRefreshing = ref(false);
// 事件处理方法
/**
 * 处理开始施工按钮点击
 */
async function handleStartConstruction() {
  currentTime.value = dayjs().format("HH:mm");
  await getCurrentLocation();
  buildModalRef.value?.open();
}

/**
 * 获取当前位置信息
 */
async function getCurrentLocation() {
  try {
    isStartButtonLoading.value = true;
    const locationResult = await getCurLocation();

    if (locationResult.errMsg === "getLocation:ok") {
      currentPosition.longitude = locationResult.longitude;
      currentPosition.latitude = locationResult.latitude;
      currentPosition.address = await reverseGeocode(
        locationResult.longitude,
        locationResult.latitude
      );
    }
  } catch (error) {
    uni.showToast({
      icon: "none",
      title: `获取定位失败: ${error}`,
    });
  } finally {
    isStartButtonLoading.value = false;
  }
}

/**
 * 处理施工确认弹窗回调事件
 * @param {Object} event - 回调事件对象
 */
async function handleBuildModalCallback(event) {
  switch (event.type) {
    case "onStart":
      console.log("开始施工");
      await executeStartConstruction();
      break;
    case "onRefreshLocation":
      isLocationRefreshing.value = true;
      await getCurrentLocation();
      isLocationRefreshing.value = false;
      break;
    case "onCancel":
      buildModalRef.value?.close();
      remarkContent.value = "";
      break;
    case "onRemark":
      remarkModalRef.value?.open();
      break;
    default:
      console.warn("未知的回调事件类型:", event.type);
      break;
  }
}
/**
 * 执行开始施工操作
 */
async function executeStartConstruction() {
  try {
    isCancelDisabled.value = true;

    // 验证位置信息
    if (!currentPosition.longitude || !currentPosition.latitude) {
      uni.showToast({
        icon: "none",
        title: "请先获取当前定位",
      });
      return;
    }

    // 构建请求参数
    const requestParams = {
      workId: props.repairWorkOrderBaseInfo?.id,
      remark: remarkContent.value,
      address: currentPosition.address,
      longitude: String(currentPosition.longitude || ""),
      latitude: String(currentPosition.latitude || ""),
    };

    console.log("开始施工请求参数:", requestParams);

    // 发送开始施工请求
    await RoutineMaintenanceService.startConstructionConfirm(requestParams);

    // 显示成功提示
    uni.showToast({
      icon: "success",
      title: "施工作业开始",
    });

    // 关闭弹窗并清空备注
    buildModalRef.value?.close();
    remarkContent.value = "";

    // 延迟刷新工单基础信息
    setTimeout(() => {
      uni.$emit("refreshWorkOrderBaseInfo");
    }, 2000);

  } catch (error) {
    console.error("开始施工请求失败:", error);
    uni.showToast({
      icon: "error",
      title: "开始施工失败，请重试",
    });
  } finally {
    isCancelDisabled.value = false;
  }
}

/**
 * 处理备注弹窗回调事件
 * @param {Object} result - 备注结果对象
 */
function handleRemarkCallback(result) {
  console.log("备注回调:", result.remark);

  if (!result.remark?.trim()) {
    uni.showToast({
      icon: "none",
      title: "请填写备注内容",
    });
    return;
  }

  remarkContent.value = result.remark;
  remarkModalRef.value?.close();
}
</script>
<style lang="scss" scoped>
@import "../common.scss";

// 主容器样式
.start-construction-container {
  background-color: #f4f8ff;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

// 主要内容区域样式
.main-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding: 0 40rpx;
  box-sizing: border-box;
}

// 底部操作按钮区域样式
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background: #f4f8ff;
  padding: 40rpx;

  // 确保按钮区域在最上层
  z-index: 100;

  // 添加上边框阴影效果
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
</style>

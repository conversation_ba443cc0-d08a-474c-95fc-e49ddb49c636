<template>
  <view class="repair-form-card">
    <view class="repair-form-title-row">
      <view class="repair-blue-bar"></view>
      <view class="repair-form-title">{{ title }}</view>
      <image
        v-if="required"
        class="repair-required-icon"
        src="/static/icon/required_icon.png"
      />
      <image
        v-if="showImproveIcon"
        class="repair-improve-data-icon"
        src="@/static/icon/improve_data_icon.png"
      />
      <image
        v-if="showAddButton && !isViewMode"
        src="@/static/image/repair_from_add.png"
        class="repair-add-btn-img"
        @click.stop="$emit('add')"
      />
    </view>
    <view class="repair-form-content">
      <slot />
    </view>
  </view>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true,
  },
  required: {
    type: Boolean,
    default: false,
  },
  showImproveIcon: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  showAddButton: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  isViewMode: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
});

defineEmits(['add']);
</script>

<style lang="scss" scoped>
.repair-form-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 16rpx 0;

  .repair-form-title-row {
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    height: 64rpx;
    position: relative;

    .repair-blue-bar {
      width: 6rpx;
      height: 28rpx;
      background: #4378ff;
      border-radius: 4rpx;
      margin-right: 12rpx;
    }
    .repair-form-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #373737;
    }
    .repair-required-icon {
      width: 84rpx;
      height: 40rpx;
      margin-left: 8rpx;
    }
    .repair-improve-data-icon {
      width: 152rpx;
      height: 40rpx;
      margin-left: 8rpx;
    }
    .repair-add-btn-img {
      width: 44rpx;
      height: 44rpx;
      margin-left: auto;
      margin-right: 0;
      display: block;
      cursor: pointer;
    }
  }
  .repair-form-content {
    padding: 0 24rpx;
    background: #fff;
  }
}
</style>

<template>
  <view class="work-order-detail-container">
    <!-- 动态组件渲染，避免多个v-if判断 -->
    <component
      :is="currentStageComponent"
      v-bind="currentStageProps"
      @getRepairChildWorkOrderDetail="handleGetChildWorkOrderDetail"
    />

    <!-- 加载骨架屏 -->
    <uv-skeleton
      v-if="isPageLoading"
      class="skeleton"
      rows="12"
      :loading="isPageLoading"
    />
  </view>
</template>

<script setup>
import { ref, watch, provide, onMounted, onUnmounted, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { RoutineMaintenanceService } from "@/service";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

// 动态导入组件模块
import i_1Confirm from "./modules/i_1Confirm.vue";
import i_2StartConstruction from "./modules/i_2StartConstruction.vue";
import i_3UnderConstruction from "./modules/i_3UnderConstruction.vue";
import i_4Verification from "./modules/i_4Verification.vue";
import i_5Completed from "./modules/i_5Completed.vue";

// ==================== 常量定义 ====================
/**
 * 工单状态映射配置
 * 定义工单各个状态的名称、样式类名和对应组件
 */
const WORK_STATUS_CONFIG = {
  1: { name: "待确认", class: "pending", component: i_1Confirm },
  2: { name: "待施工", class: "to-do", component: i_2StartConstruction },
  3: { name: "施工中", class: "in-progress", component: i_3UnderConstruction },
  4: { name: "待核验", class: "verify", component: i_4Verification },
  5: { name: "已完成", class: "completed", component: i_5Completed },
};

// ==================== Store实例 ====================
const workOrderStore = useRepairWorkOrderStore();

// ==================== 响应式数据定义 ====================
const workOrderId = ref("");
const isPageLoading = ref(false);

// 工单基础信息
const workOrderBaseInfo = ref({});
// 工单详情信息
const workOrderDetail = ref({});
// 子工单列表
const childWorkOrderList = ref([]);
// 工单人机料信息
const workOrderConfigDetail = ref({});
// 子工单任务完成情况
const childWorkOrderCompleteInfo = ref({});

// ==================== API调用方法 ====================
/**
 * 获取工单基础信息
 */
const getWorkOrderBaseInfo = async () => {
  try {
    const { data } = await RoutineMaintenanceService.workOrderBaseInfo(workOrderId.value);
    workOrderStore.updateBaseInfo(data);
    workOrderBaseInfo.value = data;

    // 基础信息加载完成后，立即加载对应状态的数据
    if (data?.workStatus && data?.id) {
      await loadDataByStatus(data.workStatus, data.id);
    }
  } catch (error) {
    console.error("获取工单基础信息失败:", error);
  }
};

/**
 * 获取工单详细信息
 * @param {string} id - 工单ID
 */
const getWorkOrderDetail = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.workOrderDetail(id);
    workOrderDetail.value = data;
  } catch (error) {
    console.error("获取工单详细信息失败:", error);
  }
};

/**
 * 获取子工单列表
 * @param {string} id - 工单ID
 */
const getChildOrderDetailList = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.getChildOrder(id);
    childWorkOrderList.value = data;
  } catch (error) {
    console.error("获取子工单列表失败:", error);
  }
};

/**
 * 获取工单维修配置
 * @param {string} id - 工单ID
 */
const getWorkOrderConfigDetail = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.getCostInfo(id);
    workOrderConfigDetail.value = data;
  } catch (error) {
    console.error("获取工单维修配置失败:", error);
  }
};

/**
 * 获取子工单任务完成情况
 * @param {string} id - 工单ID
 */
const getChildWorkOrderCompleteInfo = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.workOrderCompleteInfo(id);
    childWorkOrderCompleteInfo.value = data;
  } catch (error) {
    console.error("获取子工单任务完成情况失败:", error);
  }
};

// ==================== 状态管理配置 ====================
/**
 * 不同工单状态需要加载的数据配置
 */
const STATUS_DATA_CONFIG = {
  1: ["childOrderDetailList"], // 待确认
  2: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail"], // 待施工
  3: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail", "childWorkOrderCompleteInfo"], // 施工中
  4: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail", "childWorkOrderCompleteInfo"], // 待核验
  5: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail", "childWorkOrderCompleteInfo"], // 已完成
};

/**
 * 数据加载方法映射
 */
const DATA_LOADERS = {
  workOrderDetail: getWorkOrderDetail,
  workOrderConfigDetail: getWorkOrderConfigDetail,
  childOrderDetailList: getChildOrderDetailList,
  childWorkOrderCompleteInfo: getChildWorkOrderCompleteInfo,
};

// ==================== 计算属性 ====================
/**
 * 当前工单状态对应的组件
 */
const currentStageComponent = computed(() => {
  const status = Number(workOrderBaseInfo.value.workStatus);
  return WORK_STATUS_CONFIG[status]?.component || null;
});

/**
 * 当前阶段组件的props
 */
const currentStageProps = computed(() => {
  const baseProps = {
    repairWorkOrderBaseInfo: workOrderBaseInfo.value,
    repairChildWorkOrderList: childWorkOrderList.value,
  };

  // 确保状态为数字类型进行比较
  const status = Number(workOrderBaseInfo.value.workStatus);

  // 调试信息
  console.log('currentStageProps computed:', {
    status,
    workOrderBaseInfo: workOrderBaseInfo.value,
    childWorkOrderList: childWorkOrderList.value,
    workOrderDetail: workOrderDetail.value,
    workOrderConfigDetail: workOrderConfigDetail.value,
    childWorkOrderCompleteInfo: childWorkOrderCompleteInfo.value
  });

  // 根据不同状态添加特定的props
  switch (status) {
    case 1: // 待确认
      return {
        ...baseProps,
      };
    case 2: // 待施工
      return {
        ...baseProps,
        repairWorkOrderDetail: workOrderDetail.value,
        workOrderConfigDetail: workOrderConfigDetail.value,
      };
    case 3: // 施工中
      return {
        ...baseProps,
        repairWorkOrderDetail: workOrderDetail.value,
        workOrderConfigDetail: workOrderConfigDetail.value,
        childWorkOrderCompleteInfo: childWorkOrderCompleteInfo.value,
      };
    case 4: // 待核验
    case 5: // 已完成
      return {
        ...baseProps,
        repairWorkOrderDetail: workOrderDetail.value,
        workOrderConfigDetail: workOrderConfigDetail.value,
        childWorkOrderCompleteInfo: childWorkOrderCompleteInfo.value,
      };
  }
});

/**
 * 获取工单状态样式类
 * @param {number} status - 工单状态
 * @returns {string} CSS类名
 */
const getWorkStatusClass = (status) => WORK_STATUS_CONFIG[status]?.class;

// ==================== 数据加载逻辑 ====================
/**
 * 根据工单状态加载对应数据
 * @param {string|number} status - 工单状态
 * @param {string} workOrderId - 工单ID
 */
const loadDataByStatus = async (status, workOrderId) => {
  if (!status || !workOrderId) return;

  isPageLoading.value = true;

  try {
    const dataTypes = STATUS_DATA_CONFIG[status] || [];

    // 并行加载数据以提高性能
    const loadPromises = dataTypes.map(async (dataType) => {
      const loader = DATA_LOADERS[dataType];
      if (loader) {
        await loader(workOrderId);
      }
    });

    await Promise.all(loadPromises);
  } catch (error) {
    console.error("加载工单数据失败:", error);
  } finally {
    isPageLoading.value = false;
  }
};

// ==================== 监听器 ====================
/**
 * 监听工单状态变化，动态加载相关数据
 * 注意：这个监听器主要用于状态变化时的数据更新
 * 初始数据加载在 getWorkOrderBaseInfo 中处理
 */
watch(
  () => workOrderBaseInfo.value.workStatus,
  async (newStatus, oldStatus) => {
    const workOrderId = workOrderBaseInfo.value?.id;
    // 只有当状态真正发生变化时才重新加载数据
    if (newStatus && workOrderId && newStatus !== oldStatus) {
      await loadDataByStatus(newStatus, workOrderId);
    }
  },
  { immediate: false }
);

// ==================== 事件处理 ====================
/**
 * 处理获取子工单详情事件
 * @param {string} eventId - 事件ID
 */
const handleGetChildWorkOrderDetail = async (eventId) => {
  await getChildWorkOrderDetail(eventId);
};

// ==================== 依赖注入 ====================
provide("getWorkStatusClass", getWorkStatusClass);

// ==================== 生命周期钩子 ====================
onMounted(() => {
  // 添加全局事件监听
  uni.$on("refreshWorkOrderBaseInfo", getWorkOrderBaseInfo);
  uni.$on("refreshChildOrderDetailList", () => {
    const workOrderId = workOrderBaseInfo.value?.id;
    if (workOrderId) {
      getChildOrderDetailList(workOrderId);
    }
  });
});

onUnmounted(() => {
  // 移除全局事件监听，防止内存泄漏
  uni.$off("refreshWorkOrderBaseInfo", getWorkOrderBaseInfo);
  uni.$off("refreshChildOrderDetailList");
});

onLoad((options) => {
  if (options?.id) {
    workOrderId.value = options.id;
    getWorkOrderBaseInfo();
  }
});
</script>
<style lang="scss" scoped>
.work-order-detail-container {
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}
</style>

<template>
  <view class="top_bg" />
  <view class="content">
    <view class="main-card">
      <view class="status-card">
        <view
          v-if="!isShowAcceptanceMeasurement"
          :class="['status-label', getWorkStatusClass(baseInfo?.workStatus)]"
        >
          {{ baseInfo.workStatusName }}
        </view>
        <view class="status-header">
          <image
            class="status-icon"
            src="@/static/icon/routine_maintenance_tag.png"
          />
          <view class="status-title">
            {{ baseInfo.workCode || "-" }}
          </view>
        </view>
      </view>
      <view style="padding: 0 24rpx">
        <slot name="header"></slot>
      </view>
      <view v-show="!isHandleShow" style="padding: 0 24rpx">
        <slot name="body"></slot>
      </view>
      <view class="rectangle-bar" v-show="isShowBar">
        <image
          :class="isHandleShow ? 'down' : 'up'"
          :src="
            isHandleShow
              ? touch_down_icon
              : touch_up_icon
          "
          @click.stop="isHandleShow = !isHandleShow"
        />
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, watch, inject } from "vue";
import touch_down_icon from "@/static/icon/touch_down_icon.png";
import touch_up_icon from "@/static/icon/touch_up_icon.png";
const props = defineProps({
  baseInfo: Object,
  isShowBar: {
    type: Boolean,
    default: false,
  },
  isShowAcceptanceMeasurement: {
    type: Boolean,
    default: false,
  },
});
// 注入父组件提供的工单状态方法
const getWorkStatusClass = inject("getWorkStatusClass");
const isHandleShow = ref(false);

watch(
  () => props.isShowBar,
  (val) => {
    if (val) isHandleShow.value = true;
  },
  {
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
@import "../../common.scss";

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top_bg {
  position: fixed;
  left: 0;
  width: 100%;
  height: 100rpx;
  background: #065bff;
}
.content {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding-top: 2rpx;

  .main-card {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    background-color: #fff;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    .status-header {
      width: 100%;
      position: relative;
      text-align: center;
      box-sizing: border-box;
      margin-top: 48rpx;
      border-radius: 16rpx;
      background: #fff;
      display: flex;
      .status-icon {
        display: block;
        width: 132rpx;
        height: 54rpx;
        margin-left:-16rpx;
      }
      .status-title {
        flex: 1;
        margin-left: 20rpx;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: left;
        font-family: $font-family;
        font-weight: 500;
        font-size: 32rpx;
        color: $text-primary;
      }
    }
    .rectangle-bar {
      width: 100%;
      text-align: center;
      .down {
        width: 80rpx;
        height: 8rpx;
      }
      .up {
        width: 80rpx;
        height: 24rpx;
      }
    }
  }
}
</style>

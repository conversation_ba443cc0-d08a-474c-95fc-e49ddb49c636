<template>
  <view class="acceptance-detail-container">
    <view class="acceptance-detail-content">
      <view class="content-padding">
        <!-- 工单信息卡片 -->
        <WorkOrderWrapCard
          :base-info="workOrderBaseInfo"
          :is-show-acceptance-measurement="true"
        >
          <template #body>
            <!-- 维修描述表单 -->
            <RepairDescriptionForm
              ref="repairDescriptionFormRef"
              :detail="{ ...workOrderConfigDetail, vericaContent: workOrderDetail?.vericaContent?.[indexOfVericaContent] || {} }"
              :is-show-acceptance-measurement="true"
              :isViewMode="isViewMode"
            />
          </template>
        </WorkOrderWrapCard>
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="footer-action-area">
      <!-- 完成确认操作栏 -->
      <view class="completed-footer">
        <!-- 费用信息显示 -->
        <view class="cost-info-section">
          <!-- 实际花费 -->
          <view class="cost-item">
            <view class="cost-label">实际花费</view>
            <view class="cost-value-wrap">
              <view
                class="cost-value cost-value--actual"
                :class="{ 'cost-dash': !actualCost }"
              >
                {{ formattedActualCost }}
              </view>
              <view class="cost-unit">元</view>
            </view>
          </view>
          <!-- 预计花费 -->
          <view class="cost-item">
            <view class="cost-label cost-label--estimate">预计花费</view>
            <view class="cost-value-wrap">
              <view class="cost-value" :class="{ 'cost-dash': !estimateCost }">
                {{ formattedEstimateCost }}
              </view>
              <view class="cost-unit">元</view>
            </view>
          </view>
        </view>
        <!-- 计量完成按钮 -->
        <uv-button
          v-if="!isViewMode"
          class="confirm-button"
          :class="{ 'confirm-button--disabled': !isFormValid }"
          :disabled="!isFormValid || isConfirmLoading"
          :loading="isConfirmLoading"
          text="计量完成"
          @click="handleConfirmOrder"
        ></uv-button>
      </view>
    </view>

    <!-- 自定义退出确认弹窗 -->
    <view
      v-if="showExitDialog"
      class="exit-dialog-overlay"
      @click="hideExitDialog"
    >
      <view class="exit-dialog" @click.stop>
        <view class="exit-dialog-title">是否确认退出</view>
        <view class="exit-dialog-content">退出后，已录入数据将丢失</view>
        <view class="exit-dialog-buttons">
          <button
            class="exit-dialog-button exit-dialog-button--confirm"
            @click="confirmExit"
          >
            确认退出
          </button>
          <button
            class="exit-dialog-button exit-dialog-button--cancel"
            @click="hideExitDialog"
          >
            我再想想
          </button>
        </view>
      </view>
    </view>

    <!-- 页面加载状态 -->
    <uv-skeleton class="skeleton" rows="12" :loading="isPageLoading" />
  </view>
</template>

<script setup>
// ==================== 导入依赖 ====================
// Vue相关
import { ref, computed } from "vue";
import { onLoad, onBackPress } from "@dcloudio/uni-app";

// 第三方库
import Decimal from "decimal.js";

// 组件导入
import WorkOrderWrapCard from "../components/card/WorkOrderWrapCard.vue";
import RepairDescriptionForm from "../components/repairDescriptionForm/index.vue";

// 服务和Store导入
import { RoutineMaintenanceService } from "@/service";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

// ==================== Store实例 ====================
const workOrderStore = useRepairWorkOrderStore();

// ==================== 响应式数据 ====================
const repairDescriptionFormRef = ref(null); // 维修描述表单引用
const workOrderDetail = ref({}); // 工单详情
const workOrderConfigDetail = ref({}); // 工单配置详情
const isPageLoading = ref(false); // 页面加载状态
const showExitDialog = ref(false); // 控制退出确认弹窗显示
const isConfirmLoading = ref(false); // 确认按钮加载状态
const isViewMode = ref(false); // 查看模式
const indexOfVericaContent = ref(0); // 验收内容索引

// ==================== 计算属性 ====================
// 从store获取工单基础信息
const workOrderBaseInfo = computed(() => workOrderStore || {});

/**
 * 表单验证状态
 * 用于控制计量完成按钮的可用状态
 */
const isFormValid = computed(() => {
  return repairDescriptionFormRef.value?.isFormValid || false;
});

// ==================== 页面生命周期 ====================
/**
 * 页面加载时获取工单ID并初始化数据
 */ 
onLoad((options) => {
  if(options.isViewMode && options.index){
    isViewMode.value = true;
    indexOfVericaContent.value = options.index;
  }
  initPageData();
});

// ==================== 数据获取方法 ====================
/**
 * 初始化页面数据
 */
const initPageData = async () => {
  isPageLoading.value = true;
  try {
    await Promise.all([getWorkOrderDetail(), getWorkOrderConfigDetail()]);
  } catch (error) {
    console.error("页面数据加载失败:", error);
    uni.showToast({
      title: "数据加载失败",
      icon: "none",
    });
  } finally {
    isPageLoading.value = false;
  }
};

/**
 * 获取工单详情
 */
const getWorkOrderDetail = async () => {
  const { data } = await RoutineMaintenanceService.workOrderDetail(
    workOrderBaseInfo.value.id
  );
  workOrderDetail.value = data;
};

/**
 * 获取工单配置详情
 */
const getWorkOrderConfigDetail = async () => {
  const { data } = await RoutineMaintenanceService.getCostInfo(
    workOrderBaseInfo.value.id
  );
  workOrderConfigDetail.value = data;
};

// ==================== 计算属性 ====================
/**
 * 累加 planTotal 的辅助函数
 * @param {Array} list - 需要累加的数据列表
 * @returns {Decimal} 累加结果
 */
function sumPlanTotal(list) {
  if (!Array.isArray(list)) return new Decimal(0);
  return list.reduce((total, item) => {
    return total.plus(new Decimal(item.planTotal || 0));
  }, new Decimal(0));
}

/**
 * 预计花费计算
 * 计算人员、机械、材料、作业内容的总费用
 */
const estimateCost = computed(() => {
  const detail = workOrderConfigDetail.value;
  if (!detail) return "0.00";

  const total = sumPlanTotal(detail.users)
    .plus(sumPlanTotal(detail.machines))
    .plus(sumPlanTotal(detail.materials))
    .plus(sumPlanTotal(detail.settlements));

  return total.toFixed(2); // 返回两位小数的字符串
});

/**
 * 实际花费计算
 * 从维修描述表单中获取实际总费用
 */
const actualCost = computed(() => {
  return repairDescriptionFormRef.value?.totalCost;
});

/**
 * 格式化费用显示
 * @param {string|number} cost - 费用数值
 * @returns {string} 格式化后的费用字符串
 */
const formatCost = (cost) => {
  if (!cost || cost === "—") return "—";

  const numCost = parseFloat(cost);

  // 超过万元时显示为万元
  if (numCost >= 10000) {
    return (numCost / 10000).toFixed(2) + '万';
  }

  // 超过千元时显示为千元
  if (numCost >= 1000) {
    return (numCost / 1000).toFixed(1) + '千';
  }

  return numCost.toFixed(2);
};

/**
 * 格式化后的预计花费
 */
const formattedEstimateCost = computed(() => {
  return formatCost(estimateCost.value);
});

/**
 * 格式化后的实际花费
 */
const formattedActualCost = computed(() => {
  return formatCost(actualCost.value);
});

// ==================== 事件处理方法 ====================
/**
 * 处理确认完成操作
 * 提交验收数据并完成工单
 */
const handleConfirmOrder = async () => {
  // 表单验证检查
  if (!isFormValid.value) {
    return;
  }

  const repairForm = repairDescriptionFormRef.value.getRepairEditFormData();
  if (!repairForm) {
    // 表单数据获取失败或验证不通过
    return;
  }

  isConfirmLoading.value = true;

  try {
    const params = {
    projectId: workOrderBaseInfo.value.projectId,
    workId: workOrderBaseInfo.value.id,
    // 构建params
    filePath: repairForm.filePath,
    remark: repairForm.remark,
    // 人员
    users: repairForm.users.map((item) => ({
      id: item.id,
      realNum: item.realNum,
      realTotal: item.realTotal,
      unitPrice: item.unitPrice,
      units: item.units,
      relevancyId: item.relevancyId,
      relevancyType: item.relevancyType,
      workId: workOrderBaseInfo.value.id,
    })),
    // 作业内容
    settlements: repairForm.settlements.map((item) => ({
      id: item.id,
      content: item.content,
      configId: item.configId,
      realNumber: item.realNumber,
      realTotal: item.realTotal,
      unitPrice: item.unitPrice,
      units: item.units,
      workOrderId: workOrderBaseInfo.value.id,
    })),
    // 材料
    materials: repairForm.materials.map((item) => ({
      id: item.id,
      realNum: item.realNum,
      realTotal: item.realTotal,
      unitPrice: item.unitPrice,
      unit: item.units,
      relevancyId: item.relevancyId,
      relevancyType: item.relevancyType,
      workId: workOrderBaseInfo.value.id,
    })),
    // 设备
    machines: repairForm.machines.map((item) => ({
      id: item.id,
      realNum: item.realNum,
      realTotal: item.realTotal,
      unitPrice: item.unitPrice,
      units: item.units,
      relevancyId: item.relevancyId,
      relevancyType: item.relevancyType,
      workId: workOrderBaseInfo.value.id,
    })),
  };

  const { data } =
    await RoutineMaintenanceService.workOrderVerification(params);
  // 处理提交结果
  if (data?.allowCommit) {
    // 提交成功
    uni.showToast({
      icon: "none",
      title: "数据提交成功",
    });
    // 使用事件总线通知上一页刷新
    uni.$emit("refreshWorkOrderBaseInfo");
    // 延迟返回上一页，让用户看到成功提示
    setTimeout(() => {
      uni.navigateBack();
    }, 2000);
  } else {
    // 提交失败，显示服务器返回的错误信息
    uni.showToast({
      icon: "none",
      title: data?.msg || "数据提交失败",
    });
  }
  } finally {
    // 无论成功失败都要重置加载状态
    isConfirmLoading.value = false;
  }
};

/**
 * 隐藏退出确认弹窗
 */
const hideExitDialog = () => {
  showExitDialog.value = false;
};

/**
 * 确认退出操作
 * 退出前通知上一个页面刷新工单基础信息
 */
const confirmExit = () => {
  showExitDialog.value = false;
  uni.navigateBack();
};

/**
 * 页面返回拦截处理
 * 当用户点击左上角返回按钮或使用手势返回时触发
 * 查看模式下不拦截返回操作
 */
onBackPress((backOptions) => {
  // 查看模式下直接允许返回
  if (isViewMode.value) {
    return false;
  }

  if (backOptions.from === "backbutton") {
    // 用户手动点击返回按钮时
    showExitDialog.value = true; // 显示确认弹窗
    return true; // 拦截返回操作
  } else if (backOptions.from === "navigateBack") {
    // 代码调用uni.navigateBack()时
    return false; // 允许返回操作
  }
});
</script>

<style lang="scss" scoped>
@import "../common.scss";

.acceptance-detail-container {
  background-color: #f4f8ff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .acceptance-detail-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;

    .content-padding {
      padding: 0 32rpx 32rpx;
    }
  }

  .footer-action-area {
    height: 160rpx;
    flex-shrink: 0;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background: transparent;

    .completed-footer {
      width: 100%;
      height: 100%;
      padding: 0 32rpx;
      box-sizing: border-box;
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12rpx;

      .cost-info-section {
        flex: 1;
        display: flex;
        flex-direction: column;

        .cost-item {
          width: 100%;
          display: flex;
          align-items: center;
          overflow: hidden;
          min-height: 60rpx; // 确保最小高度

          .cost-label {
            font-size: 28rpx;
            color: #333;
            flex-shrink: 0; // 标签不缩放

            &--estimate {
              color: #4378ff;
            }
          }

          .cost-value-wrap {
            display: flex;
            align-items: center;
            flex-shrink: 1; // 允许数值区域缩放
            min-width: 0; // 允许缩小到0

            .cost-value {
              font-size: 28rpx;
              font-weight: bold;
              color: #4378ff;
              margin-right: 8rpx;

              &--actual {
                font-size: 40rpx;
              }

              &.cost-dash {
                color: #bfc4cc;
                font-weight: normal;
              }
            }

            .cost-unit {
              font-size: 24rpx;
              color: #bfc4cc;
              flex-shrink: 0; // 单位不缩放
            }
          }
        }
      }

      :deep(.confirm-button) {
        width: 250rpx;
        height: 96rpx;

        .uv-button {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          background: #4378ff;
          color: #ffffff;
          border: none;
          outline: none;
          transition: all 0.2s ease;
          box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);

          .uv-button__text {
            font-size: 40rpx!important;
            color: #ffffff;
          }

          .uv-button__loading-text {
            font-size: 40rpx!important;
            color: #ffffff;
          }
        }

        &.confirm-button--disabled .uv-button {
          background: #e4e4e4;
          color: #999999;
          box-shadow: none;

          .uv-button__text {
            color: #999999;
          }

          .uv-button__loading-text {
            color: #999999;
          }
        }
      }
    }
  }

  // ==================== 退出确认弹窗样式 ====================
  .exit-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .exit-dialog {
    width: 80%;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 48rpx 40rpx 32rpx;
    box-sizing: border-box;

    .exit-dialog-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      text-align: center;
      margin-bottom: 24rpx;
      line-height: 50rpx;
    }

    .exit-dialog-content {
      font-size: 28rpx;
      color: #999999;
      text-align: center;
      margin-bottom: 80rpx;
      line-height: 40rpx;
    }

    .exit-dialog-buttons {
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .exit-dialog-button {
        width: 100%;
        height: 72rpx;
        line-height: 72rpx;
        border-radius: 8rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;
        outline: none;
        transition: all 0.2s ease;

        &--confirm {
          background: #e4e4e4;
          color: #373737;

          &:active {
            background: #e8e8e8;
            transform: scale(0.99);
          }
        }

        &--cancel {
          background: #4378ff;
          color: #ffffff;

          &:active {
            background: #3366ee;
            transform: scale(0.99);
          }
        }
      }
    }
  }
}
</style>

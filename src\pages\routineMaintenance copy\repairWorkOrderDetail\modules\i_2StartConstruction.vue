<template>
  <view class="container">
    <view class="content">
      <ConstructionCard
        :baseInfo="repairWorkOrderBaseInfo"
        :detailInfo="repairWorkOrderDetail"
        :childOrderList="repairChildWorkOrderList"
        :configDetail="workOrderConfigDetail"
        :pageLoading="pageLoading"
      />
    </view>
    <view class="bottom_btns">
      <!-- 需要权限校验 -->
      <uv-button
        v-if="hasPermission"
        :loading="btnLoading"
        :custom-style="primaryBtnStyle"
        :customTextStyle="primaryBtnTextStyle"
        text="开始施工"
        @click="handleStart"
      ></uv-button>
    </view>
    <buildModal
      ref="buildModalRef"
      :curTime="curTime"
      :curRemark="remarkVal"
      :curAddress="myPosition.address"
      :isCancelDisabled="isCancelDisabled"
      :isRefreshLoading="isRefreshLoading"
      @onBuildCallback="onBuildCallback"
    ></buildModal>
    <remarkModal
      ref="remarkModalRef"
      :curRemark="remarkVal"
      @onRemarkCallback="onRemarkCallback"
    ></remarkModal>
  </view>
</template>
<script setup>
import { ref, reactive, computed } from "vue";
import dayjs from "dayjs";
import buildModal from "../components/modal/BuildModal.vue";
import remarkModal from "@/components/ylg-remark-modal.vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import { getCurLocation, reverseGeocode } from "@/utils/location";
import { RoutineMaintenanceService } from "@/service";
import { useUserStore } from "@/store/user";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 工单确认信息
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Object,
    default: () => [],
  },
  // 维修配置信息
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  // 数据加载
  pageLoading: {
    type: Boolean,
    default: false,
  },
});

// 用户store
const userInfo = useUserStore();

// 权限检查计算属性
const hasPermission = computed(() => {
  // 检查资源权限
  const hasResourcePermission =
    props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairWork") ||
    false;

  // 检查当前用户是否在工单用户列表中
  const currentUserId = userInfo.id;
  const isUserInWorkOrder =
    props.workOrderConfigDetail?.users?.some(
      (user) => user.relevancyId === currentUserId
    ) || false;

  return hasResourcePermission && isUserInWorkOrder;
});

const btnLoading = ref(false);
const primaryBtnStyle = {
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378ff",
  color: " #ffffff",
};
const primaryBtnTextStyle = {
  fontSize: "40rpx",
};

const buildModalRef = ref(null);
const curTime = ref("");
const myPosition = reactive({
  longitude: "",
  latitude: "",
  address: "",
});
const handleStart = async () => {
  curTime.value = dayjs().format("HH:mm");
  await getAddress();
  buildModalRef.value.open();
};
const getAddress = async () => {
  try {
    btnLoading.value = true;
    const locationRes = await getCurLocation();
    if (locationRes.errMsg == "getLocation:ok") {
      myPosition.longitude = locationRes.longitude;
      myPosition.latitude = locationRes.latitude;
      myPosition.address = await reverseGeocode(
        locationRes.longitude,
        locationRes.latitude
      );
    }
  } catch (error) {
    uni.showToast({
      icon: "none",
      title: "获取定位失败" + error,
    });
  } finally {
    btnLoading.value = false;
  }
};
// 开始施工 弹窗回调事件
let remarkModalRef = ref(null);
let isRefreshLoading = ref(false);
const onBuildCallback = async (e) => {
  switch (e.type) {
    case "onStart":
      // 开始施工
      console.log("开始施工");
      startConstruction();
      break;
    case "onRefreshLocation":
      isRefreshLoading.value = true;
      await getAddress();
      isRefreshLoading.value = false;
      break;
    case "onCancel":
      buildModalRef.value.close();
      remarkVal.value = "";
      break;
    case "onRemark":
      remarkModalRef.value.open();
      break;
    default:
      break;
  }
};
let isCancelDisabled = ref(false);
const startConstruction = async () => {
  try {
    isCancelDisabled.value = true;
    if (!myPosition.longitude || !myPosition.latitude) {
      uni.showToast({
        icon: "none",
        title: "请先获取当前定位~",
      });
      return false;
    }
    let params = {
      workId: props.repairWorkOrderBaseInfo?.id,
      remark: remarkVal.value,
      address: myPosition.address,
      longitude: String(myPosition.longitude || ""),
      latitude: String(myPosition.latitude || ""),
    };
    console.log("检查经纬度", params);

    await RoutineMaintenanceService.startConstructionConfirm(params);
    uni.showToast({
      icon: "none",
      title: "施工作业开始",
    });
    buildModalRef.value.close();
    setTimeout(() => {
      uni.$emit("refreshWorkOrderBaseInfo");
    }, 2000);

    isCancelDisabled.value = false;
  } catch (error) {
    isCancelDisabled.value = false;
    console.log("开始施工请求失败");
  }
};

let remarkVal = ref("");
const onRemarkCallback = (envRes) => {
  console.log("remark", envRes.remark);
  if (!envRes.remark) {
    uni.showToast({
      icon: "none",
      title: "请填写备注~",
    });
    return;
  }
  remarkVal.value = envRes.remark;
  remarkModalRef.value.close();
};
</script>
<style lang="scss" scoped>
@import "../common.scss";
.container {
  background-color: #f4f8ff;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding: 0 40rpx;
  box-sizing: border-box;
}
.bottom_btns {
  position: fixed;
  bottom: 0rpx;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background: #f4f8ff;
  padding: 40rpx;
}
</style>
